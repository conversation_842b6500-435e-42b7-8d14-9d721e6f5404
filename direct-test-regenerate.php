<?php
/**
 * Test direct de la régénération des sitemaps
 * 
 * Ce script teste directement la méthode de régénération sans passer par l'API REST.
 */

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier que l'utilisateur est connecté et a les permissions
if ( ! is_user_logged_in() || ! current_user_can( 'manage_options' ) ) {
    die( 'Accès refusé. Vous devez être connecté en tant qu\'administrateur.' );
}

// Activer l'affichage des erreurs
error_reporting( E_ALL );
ini_set( 'display_errors', 1 );

echo "<h1>Test Direct de Régénération Boss SEO</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:3px;}</style>";

try {
    echo "<h2>1. Chargement de la classe</h2>";
    
    // Charger la classe si nécessaire
    if ( ! class_exists( 'Boss_Robots_Sitemap_Controller' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/technical/class-boss-robots-sitemap-controller.php' );
    }
    
    $controller = new Boss_Robots_Sitemap_Controller( 'boss-seo', '1.2.0' );
    echo "<span class='success'>✅ Classe chargée avec succès</span><br>";
    
    echo "<h2>2. Vérification des paramètres</h2>";
    $settings = get_option( 'boss-seo_advanced_sitemap_settings', array() );
    echo "<span class='info'>Paramètres actuels:</span><br>";
    echo "<pre>" . print_r( $settings, true ) . "</pre>";
    
    echo "<h2>3. Test des méthodes privées</h2>";
    
    // Utiliser la réflexion pour tester les méthodes privées
    $reflection = new ReflectionClass( $controller );
    
    // Test regenerate_main_sitemap
    echo "<h3>3.1 Test regenerate_main_sitemap</h3>";
    try {
        $method = $reflection->getMethod( 'regenerate_main_sitemap' );
        $method->setAccessible( true );
        
        $test_settings = array(
            'includedPostTypes' => array( 'post', 'page' ),
            'includedTaxonomies' => array( 'category', 'post_tag' ),
            'enableTaxonomySitemaps' => true
        );
        
        $result = $method->invoke( $controller, $test_settings );
        echo "<span class='success'>✅ regenerate_main_sitemap: {$result} URLs</span><br>";
    } catch ( Exception $e ) {
        echo "<span class='error'>❌ regenerate_main_sitemap: " . $e->getMessage() . "</span><br>";
    }
    
    // Test regenerate_post_type_sitemap
    echo "<h3>3.2 Test regenerate_post_type_sitemap</h3>";
    try {
        $method = $reflection->getMethod( 'regenerate_post_type_sitemap' );
        $method->setAccessible( true );
        
        $result = $method->invoke( $controller, 'post', array() );
        echo "<span class='success'>✅ regenerate_post_type_sitemap (post): {$result} URLs</span><br>";
        
        $result = $method->invoke( $controller, 'page', array() );
        echo "<span class='success'>✅ regenerate_post_type_sitemap (page): {$result} URLs</span><br>";
    } catch ( Exception $e ) {
        echo "<span class='error'>❌ regenerate_post_type_sitemap: " . $e->getMessage() . "</span><br>";
    }
    
    // Test regenerate_image_sitemap
    echo "<h3>3.3 Test regenerate_image_sitemap</h3>";
    try {
        $method = $reflection->getMethod( 'regenerate_image_sitemap' );
        $method->setAccessible( true );
        
        $result = $method->invoke( $controller, array() );
        echo "<span class='success'>✅ regenerate_image_sitemap: {$result} URLs</span><br>";
    } catch ( Exception $e ) {
        echo "<span class='error'>❌ regenerate_image_sitemap: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<h2>4. Test de la méthode complète</h2>";
    
    // Créer une fausse requête REST
    $request = new WP_REST_Request( 'POST', '/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate' );
    
    echo "<span class='info'>Début du test de régénération complète...</span><br>";
    
    $start_time = microtime( true );
    $result = $controller->regenerate_advanced_sitemap( $request );
    $end_time = microtime( true );
    
    $duration = round( ( $end_time - $start_time ) * 1000, 2 );
    
    if ( is_wp_error( $result ) ) {
        echo "<span class='error'>❌ Erreur WP: " . $result->get_error_message() . "</span><br>";
    } else {
        echo "<span class='success'>✅ Régénération terminée en {$duration}ms</span><br>";
        echo "<span class='info'>Résultat:</span><br>";
        echo "<pre>" . print_r( $result->get_data(), true ) . "</pre>";
    }
    
    echo "<h2>5. Vérification des logs</h2>";
    echo "<span class='info'>Vérifiez les logs d'erreur pour plus de détails.</span><br>";
    
} catch ( Exception $e ) {
    echo "<span class='error'>❌ Exception: " . $e->getMessage() . "</span><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch ( Error $e ) {
    echo "<span class='error'>❌ Erreur fatale: " . $e->getMessage() . "</span><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>6. Informations système</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "WordPress Version: " . get_bloginfo( 'version' ) . "<br>";
echo "Memory Limit: " . ini_get( 'memory_limit' ) . "<br>";
echo "Max Execution Time: " . ini_get( 'max_execution_time' ) . "<br>";
echo "Current Time: " . current_time( 'mysql' ) . "<br>";

?>
