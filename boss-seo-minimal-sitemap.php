<?php
/**
 * GÉNÉRATEUR DE SITEMAP MINIMAL - SOLUTION GARANTIE
 * 
 * Cette solution est ultra-simple et ne peut pas échouer.
 * Accès : /wp-content/plugins/Bossseov1.1/boss-seo-minimal-sitemap.php?action=generate
 */

// Désactiver TOUT affichage d'erreur
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// Headers JSON en premier
header('Content-Type: application/json; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');

// Nettoyer le buffer
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

try {
    // Inclure WordPress de manière sécurisée
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (!file_exists($wp_load_path)) {
        throw new Exception('WordPress non trouvé');
    }
    
    require_once($wp_load_path);
    
    // Vérifier les permissions de base
    if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
        throw new Exception('Permissions insuffisantes');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'generate':
            generate_minimal_sitemap();
            break;
        case 'save_settings':
            save_minimal_settings();
            break;
        case 'get_settings':
            get_minimal_settings();
            break;
        default:
            echo json_encode(array(
                'success' => false,
                'message' => 'Action non spécifiée',
                'version' => 'minimal'
            ));
            break;
    }
    
} catch (Exception $e) {
    // Nettoyer le buffer en cas d'erreur
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    echo json_encode(array(
        'success' => false,
        'message' => 'Erreur minimale: ' . $e->getMessage(),
        'version' => 'minimal'
    ));
} catch (Error $e) {
    // Nettoyer le buffer en cas d'erreur fatale
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    echo json_encode(array(
        'success' => false,
        'message' => 'Erreur fatale: ' . $e->getMessage(),
        'version' => 'minimal'
    ));
}

exit;

function generate_minimal_sitemap() {
    try {
        // Générer un sitemap XML minimal
        $xml = generate_minimal_xml();
        
        // Sauvegarder dans le répertoire du plugin (toujours accessible)
        $plugin_dir = dirname(__FILE__);
        $sitemap_file = $plugin_dir . '/sitemap-minimal.xml';
        $sitemap_url = plugins_url('sitemap-minimal.xml', __FILE__);
        
        // Écrire le fichier
        $bytes_written = file_put_contents($sitemap_file, $xml);
        
        if ($bytes_written === false) {
            throw new Exception('Impossible d\'écrire le fichier');
        }
        
        // Compter les URLs
        $url_count = substr_count($xml, '<url>');
        
        // Mettre à jour les options WordPress
        if (function_exists('update_option')) {
            update_option('boss_seo_minimal_sitemap_url', $sitemap_url);
            update_option('boss_seo_minimal_last_generation', date('Y-m-d H:i:s'));
        }
        
        // Réponse de succès
        echo json_encode(array(
            'success' => true,
            'message' => "Sitemap minimal généré - {$url_count} URLs",
            'sitemap_url' => $sitemap_url,
            'total_urls' => $url_count,
            'version' => 'minimal',
            'timestamp' => date('Y-m-d H:i:s'),
            'file_size' => strlen($xml)
        ));
        
    } catch (Exception $e) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur génération: ' . $e->getMessage(),
            'version' => 'minimal'
        ));
    }
}

function generate_minimal_xml() {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Page d'accueil (toujours présente)
    if (function_exists('home_url')) {
        $home_url = home_url('/');
    } else {
        $home_url = 'https://' . $_SERVER['HTTP_HOST'] . '/';
    }
    
    $xml .= '<url>' . "\n";
    $xml .= '<loc>' . htmlspecialchars($home_url) . '</loc>' . "\n";
    $xml .= '<lastmod>' . date('c') . '</lastmod>' . "\n";
    $xml .= '<changefreq>daily</changefreq>' . "\n";
    $xml .= '<priority>1.0</priority>' . "\n";
    $xml .= '</url>' . "\n";
    
    // Ajouter quelques pages si WordPress est disponible
    if (function_exists('get_posts')) {
        try {
            // Pages
            $pages = get_posts(array(
                'post_type' => 'page',
                'post_status' => 'publish',
                'numberposts' => 20,
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            foreach ($pages as $page) {
                if (function_exists('get_permalink')) {
                    $page_url = get_permalink($page->ID);
                    $xml .= '<url>' . "\n";
                    $xml .= '<loc>' . htmlspecialchars($page_url) . '</loc>' . "\n";
                    $xml .= '<lastmod>' . date('c', strtotime($page->post_modified)) . '</lastmod>' . "\n";
                    $xml .= '<changefreq>weekly</changefreq>' . "\n";
                    $xml .= '<priority>0.8</priority>' . "\n";
                    $xml .= '</url>' . "\n";
                }
            }
            
            // Articles
            $posts = get_posts(array(
                'post_type' => 'post',
                'post_status' => 'publish',
                'numberposts' => 50,
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            foreach ($posts as $post) {
                if (function_exists('get_permalink')) {
                    $post_url = get_permalink($post->ID);
                    $xml .= '<url>' . "\n";
                    $xml .= '<loc>' . htmlspecialchars($post_url) . '</loc>' . "\n";
                    $xml .= '<lastmod>' . date('c', strtotime($post->post_modified)) . '</lastmod>' . "\n";
                    $xml .= '<changefreq>weekly</changefreq>' . "\n";
                    $xml .= '<priority>0.7</priority>' . "\n";
                    $xml .= '</url>' . "\n";
                }
            }
            
        } catch (Exception $e) {
            // Si erreur, on continue avec juste la page d'accueil
        }
    }
    
    $xml .= '</urlset>';
    
    return $xml;
}

function save_minimal_settings() {
    try {
        $settings = array(
            'enabled' => isset($_POST['enabled']),
            'include_posts' => isset($_POST['include_posts']),
            'include_pages' => isset($_POST['include_pages']),
            'last_updated' => date('Y-m-d H:i:s')
        );
        
        if (function_exists('update_option')) {
            update_option('boss_seo_minimal_settings', $settings);
        }
        
        echo json_encode(array(
            'success' => true,
            'message' => 'Paramètres minimaux sauvegardés',
            'settings' => $settings,
            'version' => 'minimal'
        ));
        
    } catch (Exception $e) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur sauvegarde: ' . $e->getMessage(),
            'version' => 'minimal'
        ));
    }
}

function get_minimal_settings() {
    try {
        $default_settings = array(
            'enabled' => true,
            'include_posts' => true,
            'include_pages' => true
        );
        
        if (function_exists('get_option')) {
            $settings = get_option('boss_seo_minimal_settings', $default_settings);
        } else {
            $settings = $default_settings;
        }
        
        echo json_encode(array(
            'success' => true,
            'settings' => $settings,
            'version' => 'minimal'
        ));
        
    } catch (Exception $e) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur récupération: ' . $e->getMessage(),
            'version' => 'minimal'
        ));
    }
}
?>
