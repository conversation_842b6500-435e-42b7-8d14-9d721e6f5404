<!DOCTYPE html>
<html>
<head>
    <title>Test Debug Endpoint Boss SEO</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; border-left: 4px solid #f44336; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        pre { background: #f9f9f9; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Debug Endpoint Boss SEO</h1>
    
    <button onclick="testDebugEndpoint()">Tester Debug Endpoint</button>
    <button onclick="testRegenerateEndpoint()">Tester Régén<PERSON></button>
    <button onclick="clearResults()">Effacer</button>
    
    <div id="results"></div>

    <script>
        async function testDebugEndpoint() {
            const resultsDiv = document.getElementById('results');
            
            try {
                addResult('🔍 Test du debug endpoint...', 'info');
                
                const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/debug/test-regenerate', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings?.nonce || ''
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ Debug endpoint fonctionne', 'success');
                    addResult('Informations de debug:', 'info');
                    addResult(JSON.stringify(data, null, 2), 'code');
                } else {
                    addResult('❌ Erreur debug endpoint: ' + response.status, 'error');
                    addResult(JSON.stringify(data, null, 2), 'code');
                }
                
            } catch (error) {
                addResult('❌ Erreur réseau: ' + error.message, 'error');
            }
        }
        
        async function testRegenerateEndpoint() {
            const resultsDiv = document.getElementById('results');
            
            try {
                addResult('🔄 Test de l\'endpoint de régénération...', 'info');
                
                const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings?.nonce || ''
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ Régénération réussie', 'success');
                    addResult('Résultat:', 'info');
                    addResult(JSON.stringify(data, null, 2), 'code');
                } else {
                    addResult('❌ Erreur régénération: ' + response.status, 'error');
                    addResult(JSON.stringify(data, null, 2), 'code');
                }
                
            } catch (error) {
                addResult('❌ Erreur réseau: ' + error.message, 'error');
            }
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result ' + type;
            
            if (type === 'code') {
                const pre = document.createElement('pre');
                pre.textContent = message;
                div.appendChild(pre);
            } else {
                div.textContent = message;
            }
            
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Vérifier si wpApiSettings est disponible
        if (typeof wpApiSettings === 'undefined') {
            addResult('⚠️ wpApiSettings non disponible. Assurez-vous d\'être sur une page WordPress avec l\'API REST activée.', 'error');
        }
    </script>
</body>
</html>
