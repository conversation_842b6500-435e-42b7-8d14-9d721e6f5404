{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "38fff39e60531b7492fcc7f770a644cc", "packages": [{"name": "samdark/sitemap", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/samdark/sitemap.git", "reference": "cf514750781275ad90fc9a828b4330c9c5ccba98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/samdark/sitemap/zipball/cf514750781275ad90fc9a828b4330c9c5ccba98", "reference": "cf514750781275ad90fc9a828b4330c9c5ccba98", "shasum": ""}, "require": {"ext-xmlwriter": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "autoload": {"psr-4": {"samdark\\sitemap\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}], "description": "Sitemap and sitemap index builder", "homepage": "https://github.com/samdark/sitemap", "keywords": ["Sitemap"], "support": {"issues": "https://github.com/samdark/sitemap/issues", "source": "https://github.com/samdark/sitemap"}, "funding": [{"url": "https://github.com/samdark", "type": "github"}, {"url": "https://www.patreon.com/samdark", "type": "patreon"}], "time": "2023-11-01T08:41:34+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}