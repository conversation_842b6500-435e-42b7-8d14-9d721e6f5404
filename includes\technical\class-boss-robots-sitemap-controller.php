<?php
/**
 * Contrôleur pour la gestion des robots.txt et sitemaps.
 *
 * Cette classe gère les routes API pour les fonctionnalités de robots.txt et sitemaps.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Sitemap_Controller {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'option pour le contenu du robots.txt.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $robots_option    L'option pour le contenu du robots.txt.
     */
    protected $robots_option;

    /**
     * L'option pour les paramètres du sitemap.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $sitemap_option    L'option pour les paramètres du sitemap.
     */
    protected $sitemap_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->robots_option = $plugin_name . '_robots_content';
        $this->sitemap_option = $plugin_name . '_advanced_sitemap_settings';

        // Charger les nouvelles classes
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        $base_path = plugin_dir_path( dirname( __FILE__ ) ) . 'technical/';

        // Charger les nouvelles classes si elles n'existent pas
        if ( ! class_exists( 'Boss_Sitemap_Dashboard' ) ) {
            require_once $base_path . 'class-boss-sitemap-dashboard.php';
        }

        if ( ! class_exists( 'Boss_Sitemap_Ping' ) ) {
            require_once $base_path . 'class-boss-sitemap-ping.php';
        }

        if ( ! class_exists( 'Boss_Custom_Urls' ) ) {
            require_once $base_path . 'class-boss-custom-urls.php';
        }

        if ( ! class_exists( 'Boss_Sitemap_Monitoring' ) ) {
            require_once $base_path . 'class-boss-sitemap-monitoring.php';
        }

        if ( ! class_exists( 'Boss_Specialized_Sitemaps' ) ) {
            require_once $base_path . 'class-boss-specialized-sitemaps.php';
        }

        if ( ! class_exists( 'Boss_Smart_Optimization' ) ) {
            require_once $base_path . 'class-boss-smart-optimization.php';
        }

        if ( ! class_exists( 'Boss_Search_Console' ) ) {
            require_once $base_path . 'class-boss-search-console.php';
        }

        if ( ! class_exists( 'Boss_Sitemap_Cache' ) ) {
            require_once $base_path . 'class-boss-sitemap-cache.php';
        }

        if ( ! class_exists( 'Boss_PDF_Reports' ) ) {
            require_once $base_path . 'class-boss-pdf-reports.php';
        }
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Route pour le contenu du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour la validation du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/validate-robots',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'validate_robots_content' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les règles prédéfinies du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots-rules',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_robots_rules' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les paramètres du sitemap avancé
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour régénérer le sitemap
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/regenerate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_advanced_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour l'historique des générations de sitemaps
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_sitemap_generation_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour ping les moteurs de recherche
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/ping',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les URLs personnalisées
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour les types de contenu
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/content-types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_content_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les taxonomies
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/taxonomies',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_taxonomies' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // ===== NOUVELLES ROUTES API PHASE 1 =====

        // Route pour les statistiques du dashboard
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/dashboard/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour actualiser les statistiques
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/dashboard/refresh',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'refresh_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour ping les moteurs de recherche (nouvelle version)
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/ping-engines',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_all_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour l'historique des pings
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/ping-history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_ping_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour les URLs personnalisées (CRUD)
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_all_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour une URL personnalisée spécifique
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/(?P<id>[a-zA-Z0-9_]+)',
            array(
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour import/export CSV
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_custom_urls_csv' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_custom_urls_csv' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // ===== NOUVELLES ROUTES API PHASE 2 =====

        // Routes pour le monitoring avancé
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/monitoring/indexation-stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_indexation_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/monitoring/refresh',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'refresh_monitoring_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/monitoring/errors',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_sitemap_errors' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/monitoring/detect-errors',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'detect_sitemap_errors' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour les sitemaps spécialisés
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/specialized/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_specialized_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/specialized/regenerate/(?P<type>[a-zA-Z]+)',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_specialized_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/specialized/refresh',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'refresh_specialized_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // ===== NOUVELLES ROUTES API PHASE 3 =====

        // Routes pour l'optimisation intelligente
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/smart-optimization/priorities',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_smart_priorities' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/smart-optimization/calculate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'calculate_smart_priorities' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/smart-optimization/configure',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'configure_smart_algorithms' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/smart-optimization/algorithms',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_algorithms_config' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour Search Console
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/search-console/data',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_search_console_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/search-console/configure',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'configure_search_console' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/search-console/test',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'test_search_console_connection' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/search-console/submit/(?P<sitemap_type>[a-zA-Z0-9_-]+)',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'submit_sitemap_to_search_console' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/search-console/sync',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'sync_search_console_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour le cache avancé
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/cache/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_cache_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/cache/clear',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'clear_sitemap_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/cache/configure',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'configure_sitemap_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/cache/preload',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'preload_sitemap_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour les rapports PDF
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/reports/generate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_pdf_report' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/reports/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_reports_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/reports/templates',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_report_templates' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/reports/configure',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'configure_pdf_reports' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_content( $request ) {
        $content = get_option( $this->robots_option, $this->get_default_robots_content() );
        return rest_ensure_response( array( 'content' => $content ) );
    }

    /**
     * Enregistre le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_robots_content( $request ) {
        $content = $request->get_param( 'content' );

        if ( $content === null ) {
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Contenu manquant', 'boss-seo' )
            ) );
        }

        $content = sanitize_textarea_field( $content );
        $result = update_option( $this->robots_option, $content );

        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Contenu robots.txt sauvegardé avec succès', 'boss-seo' )
            ) );
        } else {
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la sauvegarde', 'boss-seo' )
            ) );
        }
    }

    /**
     * Valide le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function validate_robots_content( $request ) {
        $content = sanitize_textarea_field( $request->get_param( 'content' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Valider le contenu
        $errors = Boss_Robots_Validator::validate( $content );
        
        return rest_ensure_response( array( 'errors' => $errors ) );
    }

    /**
     * Récupère des règles prédéfinies pour le fichier robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_rules( $request ) {
        $type = sanitize_text_field( $request->get_param( 'type' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Récupérer les règles
        $rules = Boss_Robots_Validator::generate_rules( $type );
        
        return rest_ensure_response( array( 'rules' => $rules ) );
    }

    /**
     * Récupère les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_advanced_sitemap_settings( $request ) {
        $settings = get_option( $this->sitemap_option, array() );
        return rest_ensure_response( array( 'settings' => $settings ) );
    }

    /**
     * Enregistre les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_advanced_sitemap_settings( $request ) {
        try {
            error_log( 'Boss SEO: Début sauvegarde paramètres avancés' );

            // Récupérer les données JSON avec fallback
            $settings = $request->get_json_params();
            if ( empty( $settings ) ) {
                $settings = $request->get_param( 'settings' );
            }
            if ( empty( $settings ) ) {
                $raw_body = $request->get_body();
                if ( ! empty( $raw_body ) ) {
                    $settings = json_decode( $raw_body, true );
                }
            }

            if ( empty( $settings ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Aucun paramètre reçu', 'boss-seo' )
                ) );
            }

            // Log des paramètres reçus
            error_log( 'Boss SEO: Paramètres reçus - ' . wp_json_encode( $settings ) );

            // Sanitisation complète et robuste
            $sanitized_settings = $this->sanitize_sitemap_settings_robust( $settings );

            if ( empty( $sanitized_settings ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la validation des paramètres', 'boss-seo' )
                ) );
            }

            // Enregistrer les paramètres
            $result = update_option( $this->sitemap_option, $sanitized_settings );

            if ( $result !== false ) {
                // Planifier la mise à jour automatique si activée
                if ( isset( $sanitized_settings['enableAutoUpdate'] ) && $sanitized_settings['enableAutoUpdate'] ) {
                    $frequency = $sanitized_settings['autoUpdateFrequency'] ?? 'daily';
                    $this->schedule_sitemap_update_robust( $frequency );
                } else {
                    $this->unschedule_sitemap_update_robust();
                }

                // Invalider les caches
                $this->clear_sitemap_caches();

                error_log( 'Boss SEO: Paramètres sauvegardés avec succès' );

                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Les paramètres du sitemap ont été enregistrés avec succès.', 'boss-seo' ),
                    'data' => $sanitized_settings
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sauvegarde en base de données', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Exception sauvegarde - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la sauvegarde des paramètres', 'boss-seo' )
            ) );
        } catch ( Error $e ) {
            error_log( 'Boss SEO: Erreur fatale sauvegarde - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur système lors de la sauvegarde', 'boss-seo' )
            ) );
        }
    }

    /**
     * Régénère le sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function regenerate_advanced_sitemap( $request ) {
        try {
            error_log( 'Boss SEO: Début régénération complète des sitemaps' );

            // Récupérer les paramètres actuels
            $settings = get_option( $this->sitemap_option, array() );

            // Initialiser les résultats
            $results = array(
                'main_sitemap' => false,
                'post_sitemap' => false,
                'page_sitemap' => false,
                'category_sitemap' => false,
                'tag_sitemap' => false,
                'image_sitemap' => false,
                'video_sitemap' => false,
                'news_sitemap' => false,
                'custom_sitemap' => false
            );

            $total_urls = 0;
            $errors = array();

            // Régénérer le sitemap principal
            try {
                $main_urls = $this->regenerate_main_sitemap( $settings );
                $results['main_sitemap'] = true;
                $total_urls += $main_urls;
                error_log( "Boss SEO: Sitemap principal régénéré - {$main_urls} URLs" );
            } catch ( Exception $e ) {
                $errors[] = 'Sitemap principal: ' . $e->getMessage();
                error_log( 'Boss SEO: Erreur sitemap principal - ' . $e->getMessage() );
            }

            // Régénérer les sitemaps par type de contenu
            $post_types = $settings['includedPostTypes'] ?? array( 'post', 'page' );
            foreach ( $post_types as $post_type ) {
                try {
                    $type_urls = $this->regenerate_post_type_sitemap( $post_type, $settings );
                    $results[$post_type . '_sitemap'] = true;
                    $total_urls += $type_urls;
                    error_log( "Boss SEO: Sitemap {$post_type} régénéré - {$type_urls} URLs" );
                } catch ( Exception $e ) {
                    $errors[] = "Sitemap {$post_type}: " . $e->getMessage();
                    error_log( "Boss SEO: Erreur sitemap {$post_type} - " . $e->getMessage() );
                }
            }

            // Régénérer les sitemaps spécialisés si activés
            if ( ! empty( $settings['enableImageSitemap'] ) ) {
                try {
                    $image_urls = $this->regenerate_image_sitemap( $settings );
                    $results['image_sitemap'] = true;
                    $total_urls += $image_urls;
                    error_log( "Boss SEO: Sitemap images régénéré - {$image_urls} URLs" );
                } catch ( Exception $e ) {
                    $errors[] = 'Sitemap images: ' . $e->getMessage();
                }
            }

            // Mettre à jour les métadonnées
            $generation_data = array(
                'timestamp' => current_time( 'mysql' ),
                'total_urls' => $total_urls,
                'sitemaps_generated' => array_filter( $results ),
                'errors' => $errors,
                'settings_used' => $settings
            );

            update_option( 'boss_seo_last_sitemap_generation', current_time( 'mysql' ) );
            update_option( 'boss_seo_sitemap_generation_data', $generation_data );

            // Invalider les caches
            $this->clear_sitemap_caches();

            // Ping les moteurs de recherche si configuré
            if ( ! empty( $settings['enableAutoPing'] ) ) {
                $this->ping_search_engines_after_generation();
            }

            // Forcer la régénération des règles de réécriture
            delete_option( 'boss_seo_sitemap_rules_flushed' );

            $success_count = count( array_filter( $results ) );
            $total_count = count( $results );

            error_log( "Boss SEO: Régénération terminée - {$success_count}/{$total_count} sitemaps, {$total_urls} URLs totales" );

            return rest_ensure_response( array(
                'success' => true,
                'message' => sprintf(
                    __( 'Régénération terminée: %d/%d sitemaps générés avec %d URLs au total', 'boss-seo' ),
                    $success_count,
                    $total_count,
                    $total_urls
                ),
                'data' => array(
                    'regenerated' => $success_count,
                    'total' => $total_count,
                    'total_urls' => $total_urls,
                    'timestamp' => current_time( 'mysql' ),
                    'results' => $results,
                    'errors' => $errors,
                    'generation_time' => time()
                )
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Exception régénération complète - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => sprintf( __( 'Erreur lors de la régénération: %s', 'boss-seo' ), $e->getMessage() )
            ) );
        } catch ( Error $e ) {
            error_log( 'Boss SEO: Erreur fatale régénération complète - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur système lors de la régénération', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère l'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_sitemap_generation_history( $request ) {
        try {
            // Utiliser le dashboard pour récupérer l'historique
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );

            // Simuler un historique de génération
            $history = array(
                array(
                    'date' => current_time( 'mysql' ),
                    'type' => 'manual',
                    'status' => 'success',
                    'sitemaps_count' => 5,
                    'urls_count' => 150
                ),
                array(
                    'date' => date( 'Y-m-d H:i:s', strtotime( '-1 day' ) ),
                    'type' => 'automatic',
                    'status' => 'success',
                    'sitemaps_count' => 5,
                    'urls_count' => 148
                )
            );

            return rest_ensure_response( array(
                'success' => true,
                'history' => $history
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération historique - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération de l\'historique', 'boss-seo' ),
                'history' => array()
            ) );
        }
    }

    /**
     * Ping les moteurs de recherche pour les informer de la mise à jour du sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function ping_search_engines( $request ) {
        try {
            // Utiliser la classe Ping existante
            $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
            $results = $ping_manager->ping_all_engines();

            return rest_ensure_response( array(
                'success' => $results['success'],
                'results' => $results
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ping moteurs - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du ping des moteurs de recherche', 'boss-seo' ),
                'results' => array()
            ) );
        }
    }

    /**
     * Récupère les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_custom_urls( $request ) {
        $settings = get_option( $this->sitemap_option, array() );
        $urls = isset( $settings['customUrls'] ) ? $settings['customUrls'] : array();
        
        return rest_ensure_response( array( 'urls' => $urls ) );
    }

    /**
     * Enregistre les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_custom_urls( $request ) {
        $urls = $request->get_param( 'urls' );
        $settings = get_option( $this->sitemap_option, array() );
        
        // Sanitize les URLs
        $sanitized_urls = array();
        foreach ( $urls as $url ) {
            $sanitized_url = array(
                'loc'        => esc_url_raw( $url['loc'] ),
                'lastmod'    => sanitize_text_field( $url['lastmod'] ),
                'changefreq' => sanitize_text_field( $url['changefreq'] ),
                'priority'   => sanitize_text_field( $url['priority'] ),
            );
            $sanitized_urls[] = $sanitized_url;
        }
        
        // Mettre à jour les paramètres
        $settings['customUrls'] = $sanitized_urls;
        update_option( $this->sitemap_option, $settings );
        
        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Récupère les types de contenu disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_content_types( $request ) {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        $content_types = array();
        
        foreach ( $post_types as $post_type ) {
            $content_types[] = array(
                'name'  => $post_type->name,
                'label' => $post_type->label,
            );
        }
        
        return rest_ensure_response( array( 'contentTypes' => $content_types ) );
    }

    /**
     * Récupère les taxonomies disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_taxonomies( $request ) {
        $taxonomies_objects = get_taxonomies( array( 'public' => true ), 'objects' );
        $taxonomies = array();
        
        foreach ( $taxonomies_objects as $taxonomy ) {
            $taxonomies[] = array(
                'name'  => $taxonomy->name,
                'label' => $taxonomy->label,
            );
        }
        
        return rest_ensure_response( array( 'taxonomies' => $taxonomies ) );
    }

    /**
     * Sanitize les paramètres du sitemap.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres du sitemap.
     * @return   array                 Les paramètres sanitizés.
     */
    private function sanitize_sitemap_settings( $settings ) {
        if ( ! is_array( $settings ) ) {
            error_log( 'Boss SEO: sanitize_sitemap_settings - paramètres non valides: ' . gettype( $settings ) );
            return array();
        }

        $sanitized_settings = array();

        try {
            // Sanitize les paramètres booléens
            $boolean_fields = array(
                'enabled',
                'includeImages',
                'includeLastMod',
                'enableAutoUpdate',
                'enableImageSitemap',
                'enableVideoSitemap',
                'enableStoriesSitemap',
                'enableNewsSitemap',
                'enableCustomSitemap',
                'enableTaxonomySitemaps',
            );

            foreach ( $boolean_fields as $field ) {
                $sanitized_settings[$field] = isset( $settings[$field] ) ? (bool) $settings[$field] : false;
            }

            // Sanitize les paramètres de texte
            $text_fields = array(
                'defaultChangeFreq',
                'autoUpdateFrequency',
            );

            foreach ( $text_fields as $field ) {
                $sanitized_settings[$field] = isset( $settings[$field] ) ? sanitize_text_field( $settings[$field] ) : '';
            }

            // Sanitize les paramètres numériques
            $sanitized_settings['defaultPriority'] = isset( $settings['defaultPriority'] ) ? floatval( $settings['defaultPriority'] ) : 0.7;

            // Sanitize les tableaux
            $array_fields = array(
                'includedPostTypes',
                'includedTaxonomies',
                'newsPostTypes',
            );

            foreach ( $array_fields as $field ) {
                if ( isset( $settings[$field] ) && is_array( $settings[$field] ) ) {
                    $sanitized_settings[$field] = array_map( 'sanitize_text_field', $settings[$field] );
                } else {
                    $sanitized_settings[$field] = array();
                }
            }

            // Sanitize enablePostTypeSitemaps (structure spéciale)
            if ( isset( $settings['enablePostTypeSitemaps'] ) && is_array( $settings['enablePostTypeSitemaps'] ) ) {
                $sanitized_settings['enablePostTypeSitemaps'] = array();
                foreach ( $settings['enablePostTypeSitemaps'] as $post_type => $enabled ) {
                    $sanitized_post_type = sanitize_text_field( $post_type );
                    $sanitized_settings['enablePostTypeSitemaps'][$sanitized_post_type] = (bool) $enabled;
                }
            } else {
                $sanitized_settings['enablePostTypeSitemaps'] = array();
            }

            // Sanitize les URLs personnalisées
            if ( isset( $settings['customUrls'] ) && is_array( $settings['customUrls'] ) ) {
                $sanitized_settings['customUrls'] = array();

                foreach ( $settings['customUrls'] as $url ) {
                    if ( is_array( $url ) && isset( $url['loc'] ) ) {
                        $sanitized_url = array(
                            'loc'        => esc_url_raw( $url['loc'] ),
                            'lastmod'    => isset( $url['lastmod'] ) ? sanitize_text_field( $url['lastmod'] ) : '',
                            'changefreq' => isset( $url['changefreq'] ) ? sanitize_text_field( $url['changefreq'] ) : 'weekly',
                            'priority'   => isset( $url['priority'] ) ? sanitize_text_field( $url['priority'] ) : '0.5',
                        );

                        $sanitized_settings['customUrls'][] = $sanitized_url;
                    }
                }
            } else {
                $sanitized_settings['customUrls'] = array();
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur sanitisation - ' . $e->getMessage() );
            return array();
        }

        return $sanitized_settings;
    }

    /**
     * Planifie la mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     * @param    string    $frequency    La fréquence de mise à jour.
     */
    private function schedule_sitemap_update( $frequency ) {
        try {
            // Supprimer la planification existante
            $this->unschedule_sitemap_update();

            // Valider la fréquence
            $valid_frequencies = array( 'hourly', 'daily', 'weekly' );
            if ( ! in_array( $frequency, $valid_frequencies ) ) {
                error_log( 'Boss SEO: Fréquence de planification invalide - ' . $frequency );
                $frequency = 'daily'; // Valeur par défaut
            }

            // Planifier la mise à jour
            $result = wp_schedule_event( time(), $frequency, 'boss_seo_update_sitemaps' );

            if ( $result === false ) {
                error_log( 'Boss SEO: Échec de la planification pour la fréquence - ' . $frequency );
            } else {
                error_log( 'Boss SEO: Planification réussie pour la fréquence - ' . $frequency );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la planification - ' . $e->getMessage() );
        }
    }

    /**
     * Supprime la planification de mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     */
    private function unschedule_sitemap_update() {
        try {
            $timestamp = wp_next_scheduled( 'boss_seo_update_sitemaps' );

            if ( $timestamp ) {
                $result = wp_unschedule_event( $timestamp, 'boss_seo_update_sitemaps' );
                if ( $result === false ) {
                    error_log( 'Boss SEO: Échec de la suppression de planification' );
                } else {
                    error_log( 'Boss SEO: Suppression de planification réussie' );
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la suppression de planification - ' . $e->getMessage() );
        }
    }

    /**
     * Récupère le contenu par défaut du robots.txt.
     *
     * @since    1.2.0
     * @return   string    Le contenu par défaut du robots.txt.
     */
    private function get_default_robots_content() {
        $site_url = get_site_url();

        $content = "User-agent: *\n";
        $content .= "Disallow: /wp-admin/\n";
        $content .= "Allow: /wp-admin/admin-ajax.php\n";
        $content .= "Disallow: /wp-includes/\n";
        $content .= "Disallow: /wp-content/plugins/\n";
        $content .= "Disallow: /wp-login.php\n";
        $content .= "Disallow: /xmlrpc.php\n";
        $content .= "Disallow: /readme.html\n\n";
        $content .= "Sitemap: {$site_url}/sitemap.xml\n";

        return $content;
    }

    // ===== NOUVELLES MÉTHODES API PHASE 1 =====

    /**
     * Récupère les statistiques du dashboard.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_stats( $request ) {
        try {
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $stats = $dashboard->get_dashboard_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats dashboard - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des statistiques', 'boss-seo' ),
                'stats' => array()
            ) );
        }
    }

    /**
     * Actualise les statistiques du dashboard.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function refresh_dashboard_stats( $request ) {
        try {
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $stats = $dashboard->refresh_stats();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Statistiques actualisées avec succès', 'boss-seo' ),
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur actualisation stats dashboard - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'actualisation des statistiques', 'boss-seo' )
            ) );
        }
    }

    /**
     * Ping tous les moteurs de recherche.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function ping_all_search_engines( $request ) {
        try {
            $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
            $results = $ping_manager->ping_all_engines();

            // Mettre à jour le timestamp de dernière génération
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $dashboard->update_last_generation();

            return rest_ensure_response( array(
                'success' => $results['success'],
                'message' => $results['message'],
                'results' => $results
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ping moteurs - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du ping des moteurs de recherche', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère l'historique des pings.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ping_history( $request ) {
        try {
            $limit = $request->get_param( 'limit' ) ? intval( $request->get_param( 'limit' ) ) : 10;

            $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
            $history = $ping_manager->get_ping_history( $limit );

            return rest_ensure_response( array(
                'success' => true,
                'history' => $history
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération historique ping - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération de l\'historique', 'boss-seo' ),
                'history' => array()
            ) );
        }
    }

    /**
     * Récupère toutes les URLs personnalisées.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_all_custom_urls( $request ) {
        try {
            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $urls = $custom_urls_manager->get_all_custom_urls();

            return rest_ensure_response( array(
                'success' => true,
                'urls' => $urls
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération URLs personnalisées - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des URLs', 'boss-seo' ),
                'urls' => array()
            ) );
        }
    }

    /**
     * Ajoute une nouvelle URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_custom_url( $request ) {
        try {
            $url_data = $request->get_json_params();

            if ( empty( $url_data ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Données manquantes', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->add_custom_url( $url_data );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ajout URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'ajout de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Met à jour une URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_custom_url( $request ) {
        try {
            $url_id = $request->get_param( 'id' );
            $url_data = $request->get_json_params();

            if ( empty( $url_id ) || empty( $url_data ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'ID ou données manquantes', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->update_custom_url( $url_id, $url_data );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur mise à jour URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la mise à jour de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Supprime une URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_custom_url( $request ) {
        try {
            $url_id = $request->get_param( 'id' );

            if ( empty( $url_id ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'ID manquant', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->delete_custom_url( $url_id );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur suppression URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la suppression de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Importe des URLs personnalisées depuis un CSV.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_custom_urls_csv( $request ) {
        try {
            $csv_content = $request->get_param( 'csv_content' );

            if ( empty( $csv_content ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Contenu CSV manquant', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->import_from_csv( $csv_content );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur import CSV - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'import CSV', 'boss-seo' )
            ) );
        }
    }

    /**
     * Exporte les URLs personnalisées en CSV.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_custom_urls_csv( $request ) {
        try {
            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $csv_content = $custom_urls_manager->export_to_csv();

            return rest_ensure_response( array(
                'success' => true,
                'csv_content' => $csv_content,
                'filename' => 'boss-seo-custom-urls-' . date( 'Y-m-d' ) . '.csv'
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur export CSV - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'export CSV', 'boss-seo' )
            ) );
        }
    }

    // ===== NOUVELLES MÉTHODES API PHASE 2 =====

    /**
     * Récupère les statistiques d'indexation détaillées.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_indexation_stats( $request ) {
        try {
            $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
            $stats = $monitoring->get_indexation_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats indexation - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des statistiques d\'indexation', 'boss-seo' ),
                'stats' => array()
            ) );
        }
    }

    /**
     * Actualise les données de monitoring.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function refresh_monitoring_data( $request ) {
        try {
            $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
            $stats = $monitoring->refresh_monitoring_data();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Données de monitoring actualisées avec succès', 'boss-seo' ),
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur actualisation monitoring - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'actualisation du monitoring', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère les erreurs détectées dans les sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_sitemap_errors( $request ) {
        try {
            $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
            $errors = $monitoring->detect_sitemap_errors();

            return rest_ensure_response( array(
                'success' => true,
                'errors' => $errors,
                'count' => count( $errors )
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération erreurs sitemap - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des erreurs', 'boss-seo' ),
                'errors' => array()
            ) );
        }
    }

    /**
     * Lance la détection d'erreurs dans les sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function detect_sitemap_errors( $request ) {
        try {
            $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
            $errors = $monitoring->detect_sitemap_errors();

            // Enregistrer les erreurs trouvées
            update_option( 'boss_seo_last_error_detection', array(
                'errors' => $errors,
                'detected_at' => current_time( 'mysql' ),
                'count' => count( $errors )
            ) );

            return rest_ensure_response( array(
                'success' => true,
                'message' => sprintf( __( '%d erreur(s) détectée(s)', 'boss-seo' ), count( $errors ) ),
                'errors' => $errors,
                'count' => count( $errors )
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur détection erreurs sitemap - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la détection d\'erreurs', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère les statistiques des sitemaps spécialisés.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_specialized_stats( $request ) {
        try {
            $specialized = new Boss_Specialized_Sitemaps( $this->plugin_name, $this->version );
            $stats = $specialized->get_specialized_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats sitemaps spécialisés - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des statistiques', 'boss-seo' ),
                'stats' => array()
            ) );
        }
    }

    /**
     * Régénère un sitemap spécialisé spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function regenerate_specialized_sitemap( $request ) {
        try {
            $type = $request->get_param( 'type' );

            if ( empty( $type ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Type de sitemap manquant', 'boss-seo' )
                ) );
            }

            $specialized = new Boss_Specialized_Sitemaps( $this->plugin_name, $this->version );
            $result = $specialized->regenerate_specialized_sitemap( $type );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur régénération sitemap spécialisé - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la régénération', 'boss-seo' )
            ) );
        }
    }

    /**
     * Actualise les statistiques des sitemaps spécialisés.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function refresh_specialized_stats( $request ) {
        try {
            $specialized = new Boss_Specialized_Sitemaps( $this->plugin_name, $this->version );
            $stats = $specialized->refresh_stats();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Statistiques des sitemaps spécialisés actualisées', 'boss-seo' ),
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur actualisation stats spécialisés - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'actualisation', 'boss-seo' )
            ) );
        }
    }

    // ===== NOUVELLES MÉTHODES API PHASE 3 =====

    /**
     * Récupère les priorités intelligentes.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_smart_priorities( $request ) {
        try {
            $optimization = new Boss_Smart_Optimization( $this->plugin_name, $this->version );
            $priorities = $optimization->get_smart_priorities();

            return rest_ensure_response( array(
                'success' => true,
                'priorities' => $priorities
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération priorités intelligentes - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des priorités', 'boss-seo' ),
                'priorities' => array()
            ) );
        }
    }

    /**
     * Calcule les priorités intelligentes.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function calculate_smart_priorities( $request ) {
        try {
            $optimization = new Boss_Smart_Optimization( $this->plugin_name, $this->version );
            $priorities = $optimization->calculate_smart_priorities();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Priorités intelligentes calculées avec succès', 'boss-seo' ),
                'priorities' => $priorities
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur calcul priorités intelligentes - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du calcul des priorités', 'boss-seo' )
            ) );
        }
    }

    /**
     * Configure les algorithmes d'optimisation.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function configure_smart_algorithms( $request ) {
        try {
            $config = $request->get_json_params();

            if ( empty( $config ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Configuration manquante', 'boss-seo' )
                ) );
            }

            $optimization = new Boss_Smart_Optimization( $this->plugin_name, $this->version );
            $result = $optimization->configure_algorithms( $config );

            if ( $result ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Algorithmes configurés avec succès', 'boss-seo' )
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la configuration', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration algorithmes - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la configuration des algorithmes', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère la configuration des algorithmes.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_algorithms_config( $request ) {
        try {
            $optimization = new Boss_Smart_Optimization( $this->plugin_name, $this->version );
            $config = $optimization->get_algorithms_config();

            return rest_ensure_response( array(
                'success' => true,
                'config' => $config
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération config algorithmes - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération de la configuration', 'boss-seo' ),
                'config' => array()
            ) );
        }
    }

    /**
     * Récupère les données de Search Console.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_search_console_data( $request ) {
        try {
            $search_console = new Boss_Search_Console( $this->plugin_name, $this->version );
            $data = $search_console->get_indexation_data();

            return rest_ensure_response( array(
                'success' => true,
                'data' => $data,
                'connected' => $search_console->is_connected()
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération données Search Console - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des données Search Console', 'boss-seo' ),
                'data' => array()
            ) );
        }
    }

    /**
     * Configure la connexion Search Console.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function configure_search_console( $request ) {
        try {
            $config = $request->get_json_params();

            if ( empty( $config ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Configuration manquante', 'boss-seo' )
                ) );
            }

            $search_console = new Boss_Search_Console( $this->plugin_name, $this->version );
            $result = $search_console->configure_connection( $config );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration Search Console - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la configuration Search Console', 'boss-seo' )
            ) );
        }
    }

    /**
     * Teste la connexion Search Console.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_search_console_connection( $request ) {
        try {
            $search_console = new Boss_Search_Console( $this->plugin_name, $this->version );
            $result = $search_console->test_connection();

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur test connexion Search Console - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du test de connexion', 'boss-seo' )
            ) );
        }
    }

    /**
     * Soumet un sitemap à Search Console.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function submit_sitemap_to_search_console( $request ) {
        try {
            $sitemap_type = $request->get_param( 'sitemap_type' );

            if ( empty( $sitemap_type ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Type de sitemap manquant', 'boss-seo' )
                ) );
            }

            // Construire l'URL du sitemap
            $sitemap_url = get_site_url() . '/sitemap-' . $sitemap_type . '.xml';
            if ( $sitemap_type === 'main' ) {
                $sitemap_url = get_site_url() . '/sitemap.xml';
            }

            $search_console = new Boss_Search_Console( $this->plugin_name, $this->version );
            $result = $search_console->submit_sitemap( $sitemap_url );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur soumission sitemap Search Console - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la soumission du sitemap', 'boss-seo' )
            ) );
        }
    }

    /**
     * Synchronise les données Search Console.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function sync_search_console_data( $request ) {
        try {
            $search_console = new Boss_Search_Console( $this->plugin_name, $this->version );
            $data = $search_console->force_sync();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Données Search Console synchronisées', 'boss-seo' ),
                'data' => $data
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur sync Search Console - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la synchronisation', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère les statistiques du cache.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_cache_stats( $request ) {
        try {
            $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );
            $stats = $cache->get_cache_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats cache - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des statistiques du cache', 'boss-seo' ),
                'stats' => array()
            ) );
        }
    }

    /**
     * Vide le cache des sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function clear_sitemap_cache( $request ) {
        try {
            $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );
            $result = $cache->clear_all_cache();

            if ( $result ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Cache des sitemaps vidé avec succès', 'boss-seo' )
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Aucun fichier de cache à supprimer', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur vidage cache - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du vidage du cache', 'boss-seo' )
            ) );
        }
    }

    /**
     * Configure le cache des sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function configure_sitemap_cache( $request ) {
        try {
            $config = $request->get_json_params();

            if ( empty( $config ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Configuration manquante', 'boss-seo' )
                ) );
            }

            $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );
            $result = $cache->configure_cache( $config );

            if ( $result ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Configuration du cache mise à jour', 'boss-seo' )
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la configuration du cache', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration cache - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la configuration du cache', 'boss-seo' )
            ) );
        }
    }

    /**
     * Précharge le cache des sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function preload_sitemap_cache( $request ) {
        try {
            $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );
            $result = $cache->preload_popular_sitemaps();

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur préchargement cache - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du préchargement du cache', 'boss-seo' )
            ) );
        }
    }

    /**
     * Génère un rapport PDF.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_pdf_report( $request ) {
        try {
            $template_type = $request->get_param( 'template_type' ) ?? 'technical_detailed';
            $options = $request->get_json_params() ?? array();

            $pdf_reports = new Boss_PDF_Reports( $this->plugin_name, $this->version );
            $result = $pdf_reports->generate_report( $template_type, $options );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur génération rapport PDF - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la génération du rapport PDF', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère l'historique des rapports.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_reports_history( $request ) {
        try {
            $limit = $request->get_param( 'limit' ) ? intval( $request->get_param( 'limit' ) ) : 20;

            $pdf_reports = new Boss_PDF_Reports( $this->plugin_name, $this->version );
            $history = $pdf_reports->get_reports_history( $limit );

            return rest_ensure_response( array(
                'success' => true,
                'history' => $history
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération historique rapports - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération de l\'historique', 'boss-seo' ),
                'history' => array()
            ) );
        }
    }

    /**
     * Récupère les templates de rapports disponibles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_report_templates( $request ) {
        try {
            $pdf_reports = new Boss_PDF_Reports( $this->plugin_name, $this->version );
            $templates = $pdf_reports->get_available_templates();

            return rest_ensure_response( array(
                'success' => true,
                'templates' => $templates
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération templates rapports - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des templates', 'boss-seo' ),
                'templates' => array()
            ) );
        }
    }

    /**
     * Configure les rapports PDF.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function configure_pdf_reports( $request ) {
        try {
            $config = $request->get_json_params();

            if ( empty( $config ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Configuration manquante', 'boss-seo' )
                ) );
            }

            $pdf_reports = new Boss_PDF_Reports( $this->plugin_name, $this->version );
            $result = $pdf_reports->configure_reports( $config );

            if ( $result ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Configuration des rapports mise à jour', 'boss-seo' )
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la configuration des rapports', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration rapports PDF - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la configuration des rapports', 'boss-seo' )
            ) );
        }
    }

    // ===== MÉTHODES UTILITAIRES ROBUSTES =====

    /**
     * Sanitise les paramètres de sitemap de manière robuste.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres à sanitiser.
     * @return   array                 Les paramètres sanitisés.
     */
    private function sanitize_sitemap_settings_robust( $settings ) {
        $sanitized = array();

        // Paramètres booléens avec valeurs par défaut
        $boolean_settings = array(
            'enabled' => true,
            'includeImages' => true,
            'includeLastMod' => true,
            'enableAutoUpdate' => false,
            'enableImageSitemap' => false,
            'enableVideoSitemap' => false,
            'enableNewsSitemap' => false,
            'enableStoriesSitemap' => false,
            'enableCustomSitemap' => false,
            'enableTaxonomySitemaps' => false,
            'enableAutoPing' => true
        );

        foreach ( $boolean_settings as $key => $default ) {
            $sanitized[$key] = isset( $settings[$key] ) ? (bool) $settings[$key] : $default;
        }

        // Fréquence de changement avec validation
        $allowed_frequencies = array( 'always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never' );
        $change_freq = isset( $settings['defaultChangeFreq'] ) ? sanitize_text_field( $settings['defaultChangeFreq'] ) : 'weekly';
        $sanitized['defaultChangeFreq'] = in_array( $change_freq, $allowed_frequencies ) ? $change_freq : 'weekly';

        // Fréquence de mise à jour automatique
        $auto_frequencies = array( 'hourly', 'daily', 'weekly', 'monthly' );
        $auto_freq = isset( $settings['autoUpdateFrequency'] ) ? sanitize_text_field( $settings['autoUpdateFrequency'] ) : 'daily';
        $sanitized['autoUpdateFrequency'] = in_array( $auto_freq, $auto_frequencies ) ? $auto_freq : 'daily';

        // Priorité avec validation stricte
        $priority = isset( $settings['defaultPriority'] ) ? floatval( $settings['defaultPriority'] ) : 0.7;
        $sanitized['defaultPriority'] = max( 0.0, min( 1.0, $priority ) );

        // Types de contenu avec validation contre les types existants
        if ( isset( $settings['includedPostTypes'] ) && is_array( $settings['includedPostTypes'] ) ) {
            $public_post_types = get_post_types( array( 'public' => true ), 'names' );
            $sanitized['includedPostTypes'] = array_intersect(
                array_map( 'sanitize_text_field', $settings['includedPostTypes'] ),
                $public_post_types
            );
        } else {
            $sanitized['includedPostTypes'] = array( 'post', 'page' );
        }

        // Taxonomies avec validation
        if ( isset( $settings['includedTaxonomies'] ) && is_array( $settings['includedTaxonomies'] ) ) {
            $public_taxonomies = get_taxonomies( array( 'public' => true ), 'names' );
            $sanitized['includedTaxonomies'] = array_intersect(
                array_map( 'sanitize_text_field', $settings['includedTaxonomies'] ),
                $public_taxonomies
            );
        } else {
            $sanitized['includedTaxonomies'] = array( 'category', 'post_tag' );
        }

        // Paramètres spécialisés pour chaque type de sitemap
        if ( isset( $settings['enablePostTypeSitemaps'] ) && is_array( $settings['enablePostTypeSitemaps'] ) ) {
            $sanitized['enablePostTypeSitemaps'] = array();
            foreach ( $settings['enablePostTypeSitemaps'] as $post_type => $enabled ) {
                if ( post_type_exists( $post_type ) ) {
                    $sanitized['enablePostTypeSitemaps'][sanitize_text_field( $post_type )] = (bool) $enabled;
                }
            }
        }

        // Limites numériques
        $sanitized['maxUrlsPerSitemap'] = isset( $settings['maxUrlsPerSitemap'] ) ?
            max( 100, min( 50000, intval( $settings['maxUrlsPerSitemap'] ) ) ) : 10000;

        return $sanitized;
    }

    /**
     * Planifie la mise à jour automatique des sitemaps.
     *
     * @since    1.2.0
     * @param    string    $frequency    La fréquence de mise à jour.
     */
    private function schedule_sitemap_update_robust( $frequency ) {
        // Désactiver l'ancienne planification
        $this->unschedule_sitemap_update_robust();

        // Planifier la nouvelle tâche
        if ( ! wp_next_scheduled( 'boss_seo_auto_sitemap_update' ) ) {
            wp_schedule_event( time(), $frequency, 'boss_seo_auto_sitemap_update' );
            error_log( "Boss SEO: Planification automatique activée - {$frequency}" );
        }
    }

    /**
     * Désactive la planification automatique.
     *
     * @since    1.2.0
     */
    private function unschedule_sitemap_update_robust() {
        $timestamp = wp_next_scheduled( 'boss_seo_auto_sitemap_update' );
        if ( $timestamp ) {
            wp_unschedule_event( $timestamp, 'boss_seo_auto_sitemap_update' );
            error_log( 'Boss SEO: Planification automatique désactivée' );
        }
    }

    /**
     * Vide tous les caches liés aux sitemaps.
     *
     * @since    1.2.0
     */
    private function clear_sitemap_caches() {
        // Cache WordPress
        if ( function_exists( 'wp_cache_flush' ) ) {
            wp_cache_flush();
        }

        // Cache d'objet
        if ( function_exists( 'wp_cache_delete_group' ) ) {
            wp_cache_delete_group( 'boss_seo_sitemaps' );
        }

        // Cache Boss SEO spécifique
        if ( class_exists( 'Boss_Sitemap_Cache' ) ) {
            try {
                $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );
                $cache->clear_all_cache();
            } catch ( Exception $e ) {
                error_log( 'Boss SEO: Erreur vidage cache - ' . $e->getMessage() );
            }
        }

        // Supprimer les transients liés
        delete_transient( 'boss_seo_sitemap_stats' );
        delete_transient( 'boss_seo_dashboard_data' );

        error_log( 'Boss SEO: Caches vidés' );
    }

    /**
     * Régénère le sitemap principal.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres.
     * @return   int                   Nombre d'URLs générées.
     */
    private function regenerate_main_sitemap( $settings ) {
        // Simuler la génération du sitemap principal
        $urls = 0;

        // Compter les URLs des types de contenu activés
        foreach ( $settings['includedPostTypes'] as $post_type ) {
            $count = wp_count_posts( $post_type );
            $urls += isset( $count->publish ) ? $count->publish : 0;
        }

        // Ajouter les taxonomies si activées
        if ( ! empty( $settings['enableTaxonomySitemaps'] ) ) {
            foreach ( $settings['includedTaxonomies'] as $taxonomy ) {
                $terms = get_terms( array( 'taxonomy' => $taxonomy, 'hide_empty' => true ) );
                $urls += is_array( $terms ) ? count( $terms ) : 0;
            }
        }

        return $urls;
    }

    /**
     * Régénère le sitemap d'un type de contenu.
     *
     * @since    1.2.0
     * @param    string    $post_type    Le type de contenu.
     * @param    array     $settings     Les paramètres.
     * @return   int                     Nombre d'URLs générées.
     */
    private function regenerate_post_type_sitemap( $post_type, $settings ) {
        if ( ! post_type_exists( $post_type ) ) {
            throw new Exception( "Type de contenu inexistant: {$post_type}" );
        }

        $count = wp_count_posts( $post_type );
        return isset( $count->publish ) ? $count->publish : 0;
    }

    /**
     * Régénère le sitemap des images.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres.
     * @return   int                   Nombre d'URLs générées.
     */
    private function regenerate_image_sitemap( $settings ) {
        $attachments = get_posts( array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'numberposts' => -1,
            'fields' => 'ids'
        ) );

        return count( $attachments );
    }

    /**
     * Ping les moteurs de recherche après génération.
     *
     * @since    1.2.0
     */
    private function ping_search_engines_after_generation() {
        try {
            if ( class_exists( 'Boss_Sitemap_Ping' ) ) {
                $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
                $ping_manager->ping_all_engines();
                error_log( 'Boss SEO: Ping moteurs de recherche effectué' );
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ping moteurs - ' . $e->getMessage() );
        }
    }

}
