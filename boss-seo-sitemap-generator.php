<?php
/**
 * GÉNÉRATEUR DE SITEMAP SIMPLE ET EFFICACE
 * 
 * Utilise samdark/sitemap pour générer des sitemaps propres
 * Accès : /wp-content/plugins/Bossseov1.1/boss-seo-sitemap-generator.php?action=generate
 */

// Désactiver l'affichage des erreurs
error_reporting(0);
ini_set('display_errors', 0);

// Headers JSON pour les requêtes AJAX
if (isset($_GET['action'])) {
    header('Content-Type: application/json; charset=UTF-8');
    header('Cache-Control: no-cache, must-revalidate');
    
    // Nettoyer le buffer
    if (ob_get_level()) {
        ob_end_clean();
    }
}

try {
    // Inclure WordPress
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (!file_exists($wp_load_path)) {
        throw new Exception('WordPress non trouvé');
    }
    require_once($wp_load_path);
    
    // Vérifier les permissions
    if (!current_user_can('manage_options')) {
        throw new Exception('Permissions insuffisantes');
    }
    
    // Vérifier samdark/sitemap
    if (!file_exists(__DIR__ . '/vendor/samdark/sitemap')) {
        throw new Exception('samdark/sitemap non installé. Exécutez: composer require samdark/sitemap');
    }
    
    require_once(__DIR__ . '/vendor/autoload.php');
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'generate':
            generate_sitemap();
            break;
        case 'get_settings':
            get_sitemap_settings();
            break;
        case 'save_settings':
            save_sitemap_settings();
            break;
        default:
            if (isset($_GET['action'])) {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'Action non reconnue'
                ));
            } else {
                show_interface();
            }
            break;
    }
    
} catch (Exception $e) {
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => $e->getMessage()
        ));
    } else {
        wp_die('Erreur: ' . $e->getMessage());
    }
}

function generate_sitemap() {
    try {
        $start_time = microtime(true);
        
        // Récupérer les paramètres
        $settings = get_option('boss_seo_sitemap_settings', array(
            'include_posts' => true,
            'include_pages' => true,
            'include_categories' => false
        ));
        
        // Créer le répertoire
        $upload_dir = wp_upload_dir();
        $sitemap_dir = $upload_dir['basedir'] . '/sitemaps/';
        if (!file_exists($sitemap_dir)) {
            wp_mkdir_p($sitemap_dir);
        }
        
        // Créer l'index des sitemaps
        $index = new \samdark\sitemap\Index($sitemap_dir . 'sitemap-index.xml');
        $total_urls = 0;
        
        // === SITEMAP PRINCIPAL ===
        $main_sitemap = new \samdark\sitemap\Sitemap($sitemap_dir . 'sitemap-main.xml');
        
        // Page d'accueil
        $home_url = home_url('/');
        $main_sitemap->addItem($home_url, time(), \samdark\sitemap\Sitemap::DAILY, 1.0);
        $total_urls++;
        
        // Pages
        if ($settings['include_pages']) {
            $pages = get_posts(array(
                'post_type' => 'page',
                'post_status' => 'publish',
                'numberposts' => 500
            ));
            
            foreach ($pages as $page) {
                $page_url = get_permalink($page->ID);
                $last_mod = strtotime($page->post_modified);
                $main_sitemap->addItem($page_url, $last_mod, \samdark\sitemap\Sitemap::WEEKLY, 0.8);
                $total_urls++;
            }
        }
        
        // Articles
        if ($settings['include_posts']) {
            $posts = get_posts(array(
                'post_type' => 'post',
                'post_status' => 'publish',
                'numberposts' => 1000
            ));
            
            foreach ($posts as $post) {
                $post_url = get_permalink($post->ID);
                $last_mod = strtotime($post->post_modified);
                $main_sitemap->addItem($post_url, $last_mod, \samdark\sitemap\Sitemap::WEEKLY, 0.7);
                $total_urls++;
            }
        }
        
        // Catégories
        if ($settings['include_categories']) {
            $categories = get_categories(array('hide_empty' => true));
            foreach ($categories as $category) {
                $cat_url = get_category_link($category->term_id);
                $main_sitemap->addItem($cat_url, time(), \samdark\sitemap\Sitemap::MONTHLY, 0.5);
                $total_urls++;
            }
        }
        
        // Écrire le sitemap principal
        $main_sitemap->write();
        $index->addSitemap($upload_dir['baseurl'] . '/sitemaps/sitemap-main.xml');
        
        // Écrire l'index
        $index->write();
        
        $generation_time = round(microtime(true) - $start_time, 2);
        
        // Mettre à jour les options
        update_option('boss_seo_last_generation', current_time('mysql'));
        update_option('boss_seo_sitemap_url', $upload_dir['baseurl'] . '/sitemaps/sitemap-index.xml');
        
        echo json_encode(array(
            'success' => true,
            'message' => "Sitemap généré avec succès - {$total_urls} URLs",
            'sitemap_url' => $upload_dir['baseurl'] . '/sitemaps/sitemap-index.xml',
            'total_urls' => $total_urls,
            'generation_time' => $generation_time,
            'timestamp' => current_time('mysql')
        ));
        
    } catch (Exception $e) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur génération: ' . $e->getMessage()
        ));
    }
}

function get_sitemap_settings() {
    $default_settings = array(
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => false
    );
    
    $settings = get_option('boss_seo_sitemap_settings', $default_settings);
    
    echo json_encode(array(
        'success' => true,
        'settings' => $settings
    ));
}

function save_sitemap_settings() {
    $settings = array(
        'include_posts' => isset($_POST['include_posts']),
        'include_pages' => isset($_POST['include_pages']),
        'include_categories' => isset($_POST['include_categories'])
    );
    
    update_option('boss_seo_sitemap_settings', $settings);
    
    echo json_encode(array(
        'success' => true,
        'message' => 'Paramètres sauvegardés',
        'settings' => $settings
    ));
}

function show_interface() {
    $settings = get_option('boss_seo_sitemap_settings', array(
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => false
    ));
    
    $last_generation = get_option('boss_seo_last_generation', 'Jamais');
    $sitemap_url = get_option('boss_seo_sitemap_url', '');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Boss SEO - Générateur de Sitemap</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f1f1f1; }
            .container { background: white; padding: 30px; border-radius: 8px; max-width: 800px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { border-bottom: 1px solid #e1e5e9; padding-bottom: 20px; margin-bottom: 30px; }
            .button { background: #0073aa; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; font-size: 14px; }
            .button:hover { background: #005a87; }
            .button.secondary { background: #6c757d; }
            .button.secondary:hover { background: #545b62; }
            .success { color: #28a745; font-weight: 500; }
            .error { color: #dc3545; font-weight: 500; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #0073aa; }
            .form-group { margin: 15px 0; }
            .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
            .checkbox { margin-right: 8px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🗺️ Boss SEO - Générateur de Sitemap</h1>
                <p>Générateur simple et efficace utilisant samdark/sitemap</p>
            </div>
            
            <div class="info">
                <strong>Dernière génération :</strong> <?php echo $last_generation; ?><br>
                <?php if ($sitemap_url): ?>
                <strong>URL du sitemap :</strong> <a href="<?php echo $sitemap_url; ?>" target="_blank"><?php echo $sitemap_url; ?></a>
                <?php endif; ?>
            </div>
            
            <h2>Paramètres</h2>
            <form id="settingsForm">
                <div class="form-group">
                    <label><input type="checkbox" name="include_pages" class="checkbox" <?php checked($settings['include_pages']); ?>> Inclure les pages</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" name="include_posts" class="checkbox" <?php checked($settings['include_posts']); ?>> Inclure les articles</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" name="include_categories" class="checkbox" <?php checked($settings['include_categories']); ?>> Inclure les catégories</label>
                </div>
                <button type="button" class="button secondary" onclick="saveSettings()">💾 Sauvegarder</button>
            </form>
            
            <h2>Actions</h2>
            <button class="button" onclick="generateSitemap()">🚀 Générer le sitemap</button>
            <?php if ($sitemap_url): ?>
            <button class="button secondary" onclick="window.open('<?php echo $sitemap_url; ?>', '_blank')">👁️ Voir le sitemap</button>
            <?php endif; ?>
            
            <div id="result"></div>
        </div>
        
        <script>
        function generateSitemap() {
            document.getElementById('result').innerHTML = '<div style="color: #0073aa;">⏳ Génération en cours...</div>';
            
            fetch('?action=generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = 
                            '<div class="success">✅ ' + data.message + 
                            '<br>🔗 <a href="' + data.sitemap_url + '" target="_blank">' + data.sitemap_url + '</a></div>';
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="error">❌ Erreur: ' + error.message + '</div>';
                });
        }
        
        function saveSettings() {
            const form = document.getElementById('settingsForm');
            const formData = new FormData(form);
            formData.append('action', 'save_settings');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                });
        }
        </script>
    </body>
    </html>
    <?php
}
?>
