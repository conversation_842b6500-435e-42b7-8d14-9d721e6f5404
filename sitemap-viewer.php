<?php
/**
 * VISUALISEUR DE SITEMAP PROPRE
 * 
 * Sert le sitemap avec les bons headers XML
 * Accès : /wp-content/plugins/Bossseov1.1/sitemap-viewer.php
 */

// Désactiver l'affichage des erreurs
error_reporting(0);
ini_set('display_errors', 0);

// Headers XML appropriés
header('Content-Type: application/xml; charset=UTF-8');
header('Cache-Control: public, max-age=3600');
header('X-Robots-Tag: noindex');

// Nettoyer le buffer
if (ob_get_level()) {
    ob_end_clean();
}

try {
    // Chercher le fichier sitemap
    $sitemap_files = array(
        __DIR__ . '/sitemap-minimal.xml',
        __DIR__ . '/boss-seo-sitemap.xml',
        __DIR__ . '/sitemap.xml'
    );
    
    $sitemap_content = null;
    $found_file = null;
    
    foreach ($sitemap_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if ($content !== false && strpos($content, '<?xml') === 0) {
                $sitemap_content = $content;
                $found_file = $file;
                break;
            }
        }
    }
    
    if ($sitemap_content) {
        // Nettoyer le contenu XML
        $sitemap_content = trim($sitemap_content);
        $sitemap_content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $sitemap_content);
        
        // Vérifier que c'est du XML valide
        $old_setting = libxml_use_internal_errors(true);
        $doc = simplexml_load_string($sitemap_content);
        libxml_use_internal_errors($old_setting);
        
        if ($doc !== false) {
            echo $sitemap_content;
        } else {
            throw new Exception('XML invalide');
        }
    } else {
        throw new Exception('Aucun sitemap trouvé');
    }
    
} catch (Exception $e) {
    // Générer un sitemap minimal d'urgence
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    echo '<url>' . "\n";
    echo '<loc>https://' . $_SERVER['HTTP_HOST'] . '/</loc>' . "\n";
    echo '<lastmod>' . date('c') . '</lastmod>' . "\n";
    echo '<changefreq>daily</changefreq>' . "\n";
    echo '<priority>1.0</priority>' . "\n";
    echo '</url>' . "\n";
    echo '</urlset>';
}

exit;
?>
