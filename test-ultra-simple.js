/**
 * Test ultra-simple pour vérifier les APIs de base
 */

console.log('🔧 Test Ultra-Simple Boss SEO');
console.log('=============================');

// Test 1: API de régénération
async function testRegen() {
    console.log('\n🔄 Test Régénération Ultra-Simple...');
    
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        
        console.log(`📊 Status: ${response.status}`);
        console.log(`📋 Headers:`, Object.fromEntries(response.headers.entries()));
        
        const text = await response.text();
        console.log(`📄 Réponse (${text.length} chars):`, text);
        
        if (response.ok) {
            try {
                const json = JSON.parse(text);
                console.log('✅ JSON valide:', json);
                return true;
            } catch (e) {
                console.log('❌ JSON invalide:', e.message);
                return false;
            }
        } else {
            console.log('❌ Erreur HTTP:', response.status);
            return false;
        }
    } catch (error) {
        console.log('💥 Erreur réseau:', error);
        return false;
    }
}

// Test 2: API de sauvegarde
async function testSave() {
    console.log('\n💾 Test Sauvegarde Ultra-Simple...');
    
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({test: true})
        });
        
        console.log(`📊 Status: ${response.status}`);
        console.log(`📋 Headers:`, Object.fromEntries(response.headers.entries()));
        
        const text = await response.text();
        console.log(`📄 Réponse (${text.length} chars):`, text);
        
        if (response.ok) {
            try {
                const json = JSON.parse(text);
                console.log('✅ JSON valide:', json);
                return true;
            } catch (e) {
                console.log('❌ JSON invalide:', e.message);
                return false;
            }
        } else {
            console.log('❌ Erreur HTTP:', response.status);
            return false;
        }
    } catch (error) {
        console.log('💥 Erreur réseau:', error);
        return false;
    }
}

// Test 3: Vérification de base
async function testBasic() {
    console.log('\n🔍 Test API de Base...');
    
    try {
        const response = await fetch('/wp-json/boss-seo/v1/', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        
        console.log(`📊 Status: ${response.status}`);
        const text = await response.text();
        console.log(`📄 Réponse:`, text.substring(0, 100) + '...');
        
        return response.ok;
    } catch (error) {
        console.log('💥 Erreur:', error);
        return false;
    }
}

// Test complet
async function runUltraSimpleTest() {
    console.log('🚀 Démarrage Test Ultra-Simple...\n');
    
    // Vérifier les prérequis
    if (typeof wpApiSettings === 'undefined') {
        console.log('❌ wpApiSettings non défini');
        return false;
    }
    
    if (!wpApiSettings.nonce) {
        console.log('❌ Nonce manquant');
        return false;
    }
    
    console.log('✅ Prérequis OK');
    console.log(`🔑 Nonce: ${wpApiSettings.nonce.substring(0, 10)}...`);
    
    // Tests
    const basicOK = await testBasic();
    const regenOK = await testRegen();
    const saveOK = await testSave();
    
    // Résultats
    console.log('\n📊 === RÉSULTATS ULTRA-SIMPLES ===');
    console.log(`🔍 API de base: ${basicOK ? '✅ OK' : '❌ ERREUR'}`);
    console.log(`🔄 Régénération: ${regenOK ? '✅ OK' : '❌ ERREUR'}`);
    console.log(`💾 Sauvegarde: ${saveOK ? '✅ OK' : '❌ ERREUR'}`);
    
    const allOK = basicOK && regenOK && saveOK;
    
    if (allOK) {
        console.log('\n🎉 TOUS LES TESTS ULTRA-SIMPLES PASSENT !');
        console.log('✅ Les APIs de base fonctionnent');
        console.log('🚀 Prêt pour les fonctionnalités avancées');
    } else {
        console.log('\n⚠️ PROBLÈMES DÉTECTÉS');
        console.log('🔧 Vérifiez les logs PHP du serveur');
        console.log('📋 Consultez les détails ci-dessus');
    }
    
    return allOK;
}

// Auto-exécution
window.ultraTest = runUltraSimpleTest;
console.log('\n🎯 Fonction disponible: ultraTest()');
console.log('🚀 Exécution automatique...\n');

// Lancer le test automatiquement
runUltraSimpleTest();
