/**
 * Service pour la gestion des robots.txt et sitemaps
 *
 * Gère les communications avec l'API pour les fonctionnalités de robots.txt et sitemaps
 */

import apiFetch from '@wordpress/api-fetch';

class RobotsSitemapService {
  /**
   * R<PERSON>cupère le contenu du fichier robots.txt
   *
   * @returns {Promise} Promesse contenant le contenu du fichier robots.txt
   */
  async getRobotsContent() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/robots';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du contenu robots.txt:', error);
      throw error;
    }
  }

  /**
   * Enregistre le contenu du fichier robots.txt
   *
   * @param {string} content - Nouveau contenu du fichier robots.txt
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveRobotsContent(content) {
    try {
      const path = '/boss-seo/v1/robots-sitemap/robots';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { content }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du contenu robots.txt:', error);
      throw error;
    }
  }

  /**
   * Valide le contenu du fichier robots.txt.
   *
   * @param {string} content Le contenu du fichier robots.txt à valider.
   * @return {Promise} Une promesse qui se résout avec le résultat de la validation.
   */
  async validateRobotsContent(content) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/validate-robots',
        method: 'POST',
        data: { content },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la validation du contenu du robots.txt:', error);
      throw error;
    }
  }

  /**
   * Récupère des règles prédéfinies pour le fichier robots.txt.
   *
   * @param {string} type Le type de règles à récupérer.
   * @return {Promise} Une promesse qui se résout avec les règles prédéfinies.
   */
  async getRobotsRules(type) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/robots-rules?type=${type}`,
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des règles ${type}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du sitemap
   *
   * @returns {Promise} Promesse contenant les paramètres du sitemap
   */
  async getSitemapSettings() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du sitemap:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du sitemap
   *
   * @param {Object} settings - Nouveaux paramètres du sitemap
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveSitemapSettings(settings) {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du sitemap:', error);
      throw error;
    }
  }

  /**
   * Récupère les types de contenu disponibles pour le sitemap
   *
   * @returns {Promise} Promesse contenant les types de contenu
   */
  async getContentTypes() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/content-types';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des types de contenu:', error);
      throw error;
    }
  }

  /**
   * Récupère les taxonomies disponibles pour le sitemap
   *
   * @returns {Promise} Promesse contenant les taxonomies
   */
  async getTaxonomies() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/taxonomies';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des taxonomies:', error);
      throw error;
    }
  }

  /**
   * Régénère le sitemap
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async regenerateSitemap() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/regenerate';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques d'indexation
   *
   * @returns {Promise} Promesse contenant les statistiques d'indexation
   */
  async getIndexationStats() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/indexation-stats';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'indexation:', error);
      throw error;
    }
  }

  /**
   * Soumet le sitemap à Google
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async submitSitemapToGoogle() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/submit-google';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la soumission du sitemap à Google:', error);
      throw error;
    }
  }

  /**
   * Soumet le sitemap à Bing
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async submitSitemapToBing() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/submit-bing';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la soumission du sitemap à Bing:', error);
      throw error;
    }
  }

  /**
   * Récupère les types de contenu disponibles.
   *
   * @return {Promise} Une promesse qui se résout avec les types de contenu.
   */
  async getContentTypes() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/content-types',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des types de contenu:', error);
      throw error;
    }
  }

  /**
   * Récupère les taxonomies disponibles.
   *
   * @return {Promise} Une promesse qui se résout avec les taxonomies.
   */
  async getTaxonomies() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/taxonomies',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des taxonomies:', error);
      throw error;
    }
  }

  /**
   * Régénère le sitemap - VERSION INTELLIGENTE avec cascade de fallbacks.
   *
   * @return {Promise} Une promesse qui se résout avec le résultat de la régénération.
   */
  async regenerateSitemap() {
    try {
      console.log('🚀 Détection de la meilleure solution disponible...');

      // NIVEAU 1 : ESSAYER LA VERSION ULTRA-ROBUSTE
      try {
        console.log('🎯 Tentative version ultra-robuste...');
        const ultraResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php?action=generate', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (ultraResponse.ok) {
          const data = await ultraResponse.json();
          if (data.success) {
            console.log('🎉 Génération ultra-robuste réussie:', data);
            // Afficher les fonctionnalités avancées
            this.showAdvancedFeatures(data);
            return data;
          }
        }
      } catch (ultraError) {
        console.log('⚠️ Version ultra-robuste non disponible, essai version professionnelle...');
      }

      // NIVEAU 2 : ESSAYER LA VERSION PROFESSIONNELLE
      try {
        console.log('🚀 Tentative version professionnelle...');
        const professionalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-professional-sitemap.php?action=generate', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (professionalResponse.ok) {
          const data = await professionalResponse.json();
          if (data.success) {
            console.log('✅ Génération professionnelle réussie:', data);
            return data;
          }
        }
      } catch (professionalError) {
        console.log('⚠️ Version professionnelle non disponible, fallback vers urgence...');
      }

      // NIVEAU 3 : FALLBACK VERS LA SOLUTION D'URGENCE
      try {
        console.log('🆘 Utilisation de la solution d\'urgence...');
        const emergencyResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-emergency-sitemap.php?action=generate', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (emergencyResponse.ok) {
          const emergencyData = await emergencyResponse.json();
          if (emergencyData.success) {
            console.log('✅ Génération d\'urgence réussie:', emergencyData);
            return emergencyData;
          }
        }
      } catch (emergencyError) {
        console.log('⚠️ Solution d\'urgence échouée, utilisation solution minimale...');
      }

      // NIVEAU 4 : SOLUTION MINIMALE (GARANTIE)
      console.log('🔧 Utilisation de la solution minimale (garantie)...');
      const minimalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-minimal-sitemap.php?action=generate', {
        method: 'GET',
        credentials: 'same-origin'
      });

      if (!minimalResponse.ok) {
        throw new Error(`Toutes les solutions ont échoué! Status: ${minimalResponse.status}`);
      }

      const minimalData = await minimalResponse.json();
      console.log('✅ Génération minimale réussie:', minimalData);
      return minimalData;

    } catch (error) {
      console.error('❌ Erreur lors de la régénération du sitemap:', error);
      throw error;
    }
  }

  /**
   * Affiche les fonctionnalités avancées disponibles avec la version ultra-robuste.
   *
   * @param {Object} data - Données de la génération ultra-robuste.
   */
  showAdvancedFeatures(data) {
    if (data.analytics_url) {
      console.log('📊 Analytics disponibles:', data.analytics_url);
    }
    if (data.generation_id) {
      console.log('🆔 ID de génération:', data.generation_id);
    }
    if (data.generation_time) {
      console.log('⏱️ Temps de génération:', data.generation_time + 's');
    }
  }

  /**
   * Récupère les paramètres du sitemap avancé - VERSION PROFESSIONNELLE avec fallback.
   *
   * @return {Promise} Une promesse qui se résout avec les paramètres du sitemap.
   */
  async getAdvancedSitemapSettings() {
    try {
      console.log('📖 Récupération des paramètres...');

      // ESSAYER D'ABORD LA VERSION PROFESSIONNELLE
      try {
        const professionalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-professional-sitemap.php?action=get_settings', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (professionalResponse.ok) {
          const data = await professionalResponse.json();
          if (data.success) {
            console.log('✅ Paramètres professionnels récupérés');
            // Convertir au format attendu par l'interface
            return {
              success: true,
              data: {
                includedPostTypes: [
                  ...(data.settings.include_posts ? ['post'] : []),
                  ...(data.settings.include_pages ? ['page'] : [])
                ],
                includedTaxonomies: [
                  ...(data.settings.include_categories ? ['category'] : []),
                  ...(data.settings.include_tags ? ['post_tag'] : [])
                ],
                maxUrlsPerSitemap: data.settings.max_urls_per_sitemap || 50000,
                enableImageSitemap: false,
                enableTaxonomySitemaps: data.settings.include_categories || data.settings.include_tags
              }
            };
          }
        }
      } catch (professionalError) {
        console.log('⚠️ Erreur version professionnelle, fallback vers urgence');
      }

      // FALLBACK VERS LA SOLUTION D'URGENCE
      try {
        const emergencyResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-emergency-sitemap.php?action=get_settings', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (emergencyResponse.ok) {
          const emergencyData = await emergencyResponse.json();
          if (emergencyData.success) {
            console.log('✅ Paramètres d\'urgence récupérés');
            // Convertir au format attendu par l'interface
            return {
              success: true,
              data: {
                includedPostTypes: [
                  ...(emergencyData.settings.include_posts ? ['post'] : []),
                  ...(emergencyData.settings.include_pages ? ['page'] : [])
                ],
                includedTaxonomies: [
                  ...(emergencyData.settings.include_categories ? ['category'] : [])
                ],
                enableImageSitemap: false,
                enableTaxonomySitemaps: emergencyData.settings.include_categories
              }
            };
          }
        }
      } catch (emergencyError) {
        console.log('⚠️ Paramètres d\'urgence échoués, utilisation solution minimale...');
      }

      // SOLUTION MINIMALE (GARANTIE)
      try {
        const minimalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-minimal-sitemap.php?action=get_settings', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (minimalResponse.ok) {
          const minimalData = await minimalResponse.json();
          if (minimalData.success) {
            console.log('✅ Paramètres minimaux récupérés');
            return {
              success: true,
              data: {
                includedPostTypes: [
                  ...(minimalData.settings.include_posts ? ['post'] : []),
                  ...(minimalData.settings.include_pages ? ['page'] : [])
                ],
                includedTaxonomies: ['category'],
                enableImageSitemap: false,
                enableTaxonomySitemaps: false
              }
            };
          }
        }
      } catch (minimalError) {
        console.log('⚠️ Solution minimale échouée aussi');
      }

      // Paramètres par défaut si TOUT échoue
      console.log('⚠️ Utilisation des paramètres par défaut');
      return {
        success: true,
        data: {
          includedPostTypes: ['post', 'page'],
          includedTaxonomies: ['category'],
          enableImageSitemap: false,
          enableTaxonomySitemaps: true
        }
      };

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des paramètres:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du sitemap avancé - VERSION PROFESSIONNELLE avec fallback.
   *
   * @param {Object} settings Les paramètres du sitemap.
   * @return {Promise} Une promesse qui se résout avec le résultat de l'enregistrement.
   */
  async saveAdvancedSitemapSettings(settings) {
    try {
      console.log('💾 Sauvegarde des paramètres:', settings);

      // Préparer les données pour les deux solutions
      const formData = new FormData();
      formData.append('action', 'save_settings');

      // Convertir les paramètres en format simple
      if (settings.includedPostTypes && settings.includedPostTypes.includes('post')) {
        formData.append('include_posts', '1');
      }
      if (settings.includedPostTypes && settings.includedPostTypes.includes('page')) {
        formData.append('include_pages', '1');
      }
      if (settings.includedTaxonomies && settings.includedTaxonomies.includes('category')) {
        formData.append('include_categories', '1');
      }
      if (settings.includedTaxonomies && settings.includedTaxonomies.includes('post_tag')) {
        formData.append('include_tags', '1');
      }

      // Paramètres avancés pour la version professionnelle
      formData.append('max_urls_per_sitemap', settings.maxUrlsPerSitemap || 50000);

      // ESSAYER D'ABORD LA VERSION PROFESSIONNELLE
      try {
        const professionalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-professional-sitemap.php', {
          method: 'POST',
          body: formData,
          credentials: 'same-origin'
        });

        if (professionalResponse.ok) {
          const data = await professionalResponse.json();
          if (data.success) {
            console.log('✅ Sauvegarde professionnelle réussie');
            return data;
          }
        }
      } catch (professionalError) {
        console.log('⚠️ Erreur version professionnelle, fallback vers urgence');
      }

      // FALLBACK VERS LA SOLUTION D'URGENCE
      try {
        const emergencyResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-emergency-sitemap.php', {
          method: 'POST',
          body: formData,
          credentials: 'same-origin'
        });

        if (emergencyResponse.ok) {
          const emergencyData = await emergencyResponse.json();
          if (emergencyData.success) {
            console.log('✅ Sauvegarde d\'urgence réussie');
            return emergencyData;
          }
        }
      } catch (emergencyError) {
        console.log('⚠️ Sauvegarde d\'urgence échouée, utilisation solution minimale...');
      }

      // SOLUTION MINIMALE (GARANTIE)
      const minimalResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-minimal-sitemap.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
      });

      if (!minimalResponse.ok) {
        throw new Error(`Toutes les solutions de sauvegarde ont échoué! Status: ${minimalResponse.status}`);
      }

      const minimalData = await minimalResponse.json();
      console.log('✅ Sauvegarde minimale réussie');
      return minimalData;

    } catch (error) {
      console.error('❌ Erreur lors de l\'enregistrement des paramètres:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des générations de sitemaps.
   *
   * @return {Promise} Une promesse qui se résout avec l'historique des générations.
   */
  async getSitemapGenerationHistory() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/history',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des générations:', error);
      throw error;
    }
  }

  /**
   * Ping les moteurs de recherche - VERSION INTELLIGENTE avec cascade.
   *
   * @return {Promise} Une promesse qui se résout avec le résultat du ping.
   */
  async pingSearchEngines() {
    try {
      console.log('📤 Ping des moteurs de recherche...');

      // Essayer d'abord la version ultra-robuste
      try {
        const ultraResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php?action=ping_engines', {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (ultraResponse.ok) {
          const data = await ultraResponse.json();
          if (data.success) {
            console.log('✅ Ping ultra-robuste réussi');
            return data;
          }
        }
      } catch (ultraError) {
        console.log('⚠️ Ping ultra-robuste non disponible, fallback...');
      }

      // Fallback vers l'API REST originale
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/ping',
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du ping des moteurs de recherche:', error);
      throw error;
    }
  }

  /**
   * Récupère les URLs personnalisées pour le sitemap.
   *
   * @return {Promise} Une promesse qui se résout avec les URLs personnalisées.
   */
  async getCustomUrls() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des URLs personnalisées:', error);
      throw error;
    }
  }

  /**
   * Enregistre les URLs personnalisées pour le sitemap.
   *
   * @param {Array} urls Les URLs personnalisées.
   * @return {Promise} Une promesse qui se résout avec le résultat de l'enregistrement.
   */
  async saveCustomUrls(urls) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls',
        method: 'POST',
        data: { urls },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des URLs personnalisées:', error);
      throw error;
    }
  }

  // ===== NOUVELLES MÉTHODES API PHASE 1 =====

  /**
   * Récupère les statistiques du dashboard
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getDashboardStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/dashboard/stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Actualise les statistiques du dashboard
   *
   * @returns {Promise} Promesse contenant les nouvelles statistiques
   */
  async refreshDashboardStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/dashboard/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation des statistiques:', error);
      throw error;
    }
  }

  /**
   * Ping les moteurs de recherche (nouvelle version)
   *
   * @returns {Promise} Promesse contenant les résultats du ping
   */
  async pingAllSearchEngines() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/ping-engines',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du ping des moteurs de recherche:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des pings
   *
   * @param {number} limit - Nombre d'enregistrements à récupérer
   * @returns {Promise} Promesse contenant l'historique
   */
  async getPingHistory(limit = 10) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/ping-history?limit=${limit}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des pings:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les URLs personnalisées (nouvelle version)
   *
   * @returns {Promise} Promesse contenant les URLs
   */
  async getAllCustomUrls() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des URLs personnalisées:', error);
      throw error;
    }
  }

  /**
   * Ajoute une nouvelle URL personnalisée
   *
   * @param {Object} urlData - Données de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async addCustomUrl(urlData) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls',
        method: 'POST',
        data: urlData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Met à jour une URL personnalisée
   *
   * @param {string} urlId - ID de l'URL
   * @param {Object} urlData - Nouvelles données de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async updateCustomUrl(urlId, urlData) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/custom-urls/${urlId}`,
        method: 'PUT',
        data: urlData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Supprime une URL personnalisée
   *
   * @param {string} urlId - ID de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async deleteCustomUrl(urlId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/custom-urls/${urlId}`,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Importe des URLs depuis un CSV
   *
   * @param {string} csvContent - Contenu du CSV
   * @returns {Promise} Promesse contenant le résultat
   */
  async importCustomUrlsCsv(csvContent) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls/import',
        method: 'POST',
        data: { csv_content: csvContent }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'import CSV:', error);
      throw error;
    }
  }

  /**
   * Exporte les URLs en CSV
   *
   * @returns {Promise} Promesse contenant le CSV
   */
  async exportCustomUrlsCsv() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls/export',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
      throw error;
    }
  }

  // ===== NOUVELLES MÉTHODES API PHASE 2 =====

  /**
   * Récupère les statistiques d'indexation détaillées
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getIndexationStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/indexation-stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'indexation:', error);
      throw error;
    }
  }

  /**
   * Actualise les données de monitoring
   *
   * @returns {Promise} Promesse contenant les nouvelles données
   */
  async refreshMonitoringData() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation du monitoring:', error);
      throw error;
    }
  }

  /**
   * Récupère les erreurs détectées dans les sitemaps
   *
   * @returns {Promise} Promesse contenant les erreurs
   */
  async getSitemapErrors() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/errors',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des erreurs:', error);
      throw error;
    }
  }

  /**
   * Lance la détection d'erreurs dans les sitemaps
   *
   * @returns {Promise} Promesse contenant les erreurs détectées
   */
  async detectSitemapErrors() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/detect-errors',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la détection d\'erreurs:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques des sitemaps spécialisés
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getSpecializedStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/specialized/stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats spécialisés:', error);
      throw error;
    }
  }

  /**
   * Régénère un sitemap spécialisé spécifique
   *
   * @param {string} type - Type de sitemap (image, video, news, stories)
   * @returns {Promise} Promesse contenant le résultat
   */
  async regenerateSpecializedSitemap(type) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/specialized/regenerate/${type}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap spécialisé:', error);
      throw error;
    }
  }

  /**
   * Actualise les statistiques des sitemaps spécialisés
   *
   * @returns {Promise} Promesse contenant les nouvelles statistiques
   */
  async refreshSpecializedStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/specialized/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation des stats spécialisés:', error);
      throw error;
    }
  }

  // ===== NOUVELLES MÉTHODES API PHASE 3 =====

  // Optimisation intelligente
  async getSmartPriorities() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/smart-optimization/priorities',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des priorités intelligentes:', error);
      throw error;
    }
  }

  async calculateSmartPriorities() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/smart-optimization/calculate',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du calcul des priorités intelligentes:', error);
      throw error;
    }
  }

  async configureSmartAlgorithms(config) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/smart-optimization/configure',
        method: 'POST',
        data: config
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la configuration des algorithmes:', error);
      throw error;
    }
  }

  async getAlgorithmsConfig() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/smart-optimization/algorithms',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la config des algorithmes:', error);
      throw error;
    }
  }

  // Search Console
  async getSearchConsoleData() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/search-console/data',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données Search Console:', error);
      throw error;
    }
  }

  async configureSearchConsole(config) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/search-console/configure',
        method: 'POST',
        data: config
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la configuration Search Console:', error);
      throw error;
    }
  }

  async testSearchConsoleConnection() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/search-console/test',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du test de connexion Search Console:', error);
      throw error;
    }
  }

  async submitSitemapToSearchConsole(sitemapType) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/search-console/submit/${sitemapType}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la soumission du sitemap:', error);
      throw error;
    }
  }

  async syncSearchConsoleData() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/search-console/sync',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la synchronisation Search Console:', error);
      throw error;
    }
  }

  // Cache avancé
  async getCacheStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/cache/stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats du cache:', error);
      throw error;
    }
  }

  async clearSitemapCache() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/cache/clear',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
      throw error;
    }
  }

  async configureSitemapCache(config) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/cache/configure',
        method: 'POST',
        data: config
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la configuration du cache:', error);
      throw error;
    }
  }

  async preloadSitemapCache() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/cache/preload',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du préchargement du cache:', error);
      throw error;
    }
  }

  // Rapports PDF
  async generatePdfReport(templateType = 'technical_detailed', options = {}) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/reports/generate',
        method: 'POST',
        data: { template_type: templateType, ...options }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération du rapport PDF:', error);
      throw error;
    }
  }

  async getReportsHistory(limit = 20) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/reports/history?limit=${limit}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des rapports:', error);
      throw error;
    }
  }

  async getReportTemplates() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/reports/templates',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des templates:', error);
      throw error;
    }
  }

  async configurePdfReports(config) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/reports/configure',
        method: 'POST',
        data: config
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la configuration des rapports:', error);
      throw error;
    }
  }
  /**
   * Récupère les analytics du sitemap (version ultra-robuste uniquement).
   *
   * @return {Promise} Une promesse qui se résout avec les analytics.
   */
  async getSitemapAnalytics() {
    try {
      const response = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php?action=analytics', {
        method: 'GET',
        credentials: 'same-origin'
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error('Analytics non disponibles - Version ultra-robuste requise');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des analytics:', error);
      throw error;
    }
  }

  /**
   * Exporte le sitemap en CSV (version ultra-robuste uniquement).
   *
   * @return {Promise} Une promesse qui se résout avec le lien de téléchargement.
   */
  async exportSitemapCSV() {
    try {
      const response = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php?action=export_csv', {
        method: 'GET',
        credentials: 'same-origin'
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error('Export CSV non disponible - Version ultra-robuste requise');
      }
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
      throw error;
    }
  }

  /**
   * Détecte quelle version est disponible.
   *
   * @return {Promise} Une promesse qui se résout avec les versions disponibles.
   */
  async detectAvailableVersions() {
    const versions = {
      ultraRobust: false,
      professional: false,
      emergency: false,
      minimal: true // Toujours disponible (garantie)
    };

    try {
      // Test version ultra-robuste
      const ultraResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php', {
        method: 'HEAD',
        credentials: 'same-origin'
      });
      versions.ultraRobust = ultraResponse.ok;
    } catch (e) {
      versions.ultraRobust = false;
    }

    try {
      // Test version professionnelle
      const proResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-professional-sitemap.php', {
        method: 'HEAD',
        credentials: 'same-origin'
      });
      versions.professional = proResponse.ok;
    } catch (e) {
      versions.professional = false;
    }

    try {
      // Test version d'urgence
      const emergencyResponse = await fetch('/wp-content/plugins/Bossseov1.1/boss-seo-emergency-sitemap.php', {
        method: 'HEAD',
        credentials: 'same-origin'
      });
      versions.emergency = emergencyResponse.ok;
    } catch (e) {
      versions.emergency = false;
    }

    console.log('🔍 Versions détectées:', versions);
    return versions;
  }
}

export default new RobotsSitemapService();
