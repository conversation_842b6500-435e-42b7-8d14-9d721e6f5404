<?php
/**
 * SOLUTION D'URGENCE - Générateur de sitemap Boss SEO
 *
 * Accès direct : /wp-content/plugins/Bossseov1.1/boss-seo-emergency-sitemap.php?action=generate
 */

// Désactiver l'affichage des erreurs pour éviter les problèmes JSON
error_reporting(0);
ini_set('display_errors', 0);

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    header( 'Content-Type: application/json' );
    echo json_encode( array( 'success' => false, 'message' => 'Accès refusé' ) );
    exit;
}

$action = isset( $_GET['action'] ) ? $_GET['action'] : '';

switch ( $action ) {
    case 'generate':
        generate_emergency_sitemap();
        break;
    case 'save_settings':
        save_emergency_settings();
        break;
    case 'get_settings':
        get_emergency_settings();
        break;
    default:
        show_emergency_interface();
        break;
}

function generate_emergency_sitemap() {
    // Nettoyer le buffer de sortie pour éviter les erreurs JSON
    if (ob_get_level()) {
        ob_clean();
    }

    try {
        // Générer le sitemap
        $xml = generate_sitemap_xml();

        // Sauvegarder dans uploads
        $upload_dir = wp_upload_dir();
        if ( ! $upload_dir || isset( $upload_dir['error'] ) ) {
            // Fallback vers le répertoire du plugin
            $plugin_dir = dirname( __FILE__ );
            $sitemap_file = $plugin_dir . '/boss-seo-sitemap.xml';
            $sitemap_url = plugins_url( 'boss-seo-sitemap.xml', __FILE__ );
        } else {
            $sitemap_file = $upload_dir['basedir'] . '/boss-seo-sitemap.xml';
            $sitemap_url = $upload_dir['baseurl'] . '/boss-seo-sitemap.xml';

            // Créer le répertoire si nécessaire
            $sitemap_dir = dirname( $sitemap_file );
            if ( ! file_exists( $sitemap_dir ) ) {
                wp_mkdir_p( $sitemap_dir );
            }
        }

        if ( file_put_contents( $sitemap_file, $xml ) !== false ) {
            $url_count = substr_count( $xml, '<url>' );

            // Mettre à jour les options
            update_option( 'boss_seo_last_sitemap_generation', current_time( 'mysql' ) );
            update_option( 'boss_seo_sitemap_url', $sitemap_url );

            // Réponse JSON propre
            header( 'Content-Type: application/json; charset=UTF-8' );
            echo json_encode( array(
                'success' => true,
                'message' => "Sitemap d'urgence généré avec succès - {$url_count} URLs",
                'sitemap_url' => $sitemap_url,
                'total_urls' => $url_count,
                'version' => 'emergency',
                'timestamp' => current_time( 'mysql' )
            ), JSON_UNESCAPED_SLASHES );
        } else {
            throw new Exception( 'Impossible d\'écrire le fichier sitemap' );
        }
    } catch ( Exception $e ) {
        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur d\'urgence: ' . $e->getMessage(),
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    } catch ( Error $e ) {
        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur fatale: ' . $e->getMessage(),
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    }
    exit;
}

function save_emergency_settings() {
    // Nettoyer le buffer de sortie
    if (ob_get_level()) {
        ob_clean();
    }

    try {
        $settings = array(
            'enabled' => isset( $_POST['enabled'] ) ? true : false,
            'include_posts' => isset( $_POST['include_posts'] ) ? true : false,
            'include_pages' => isset( $_POST['include_pages'] ) ? true : false,
            'include_categories' => isset( $_POST['include_categories'] ) ? true : false,
            'last_updated' => current_time( 'mysql' )
        );

        $result = update_option( 'boss_seo_emergency_settings', $settings );

        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => true,
            'message' => 'Paramètres d\'urgence sauvegardés',
            'settings' => $settings,
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    } catch ( Exception $e ) {
        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur sauvegarde: ' . $e->getMessage(),
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    }
    exit;
}

function get_emergency_settings() {
    // Nettoyer le buffer de sortie
    if (ob_get_level()) {
        ob_clean();
    }

    try {
        $settings = get_option( 'boss_seo_emergency_settings', array(
            'enabled' => true,
            'include_posts' => true,
            'include_pages' => true,
            'include_categories' => true
        ) );

        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => true,
            'settings' => $settings,
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    } catch ( Exception $e ) {
        header( 'Content-Type: application/json; charset=UTF-8' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur récupération: ' . $e->getMessage(),
            'version' => 'emergency'
        ), JSON_UNESCAPED_SLASHES );
    }
    exit;
}

function generate_sitemap_xml() {
    $settings = get_option( 'boss_seo_emergency_settings', array(
        'enabled' => true,
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => true
    ) );
    
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Page d'accueil
    $xml .= '<url>' . "\n";
    $xml .= '<loc>' . esc_url( home_url( '/' ) ) . '</loc>' . "\n";
    $xml .= '<lastmod>' . date( 'c' ) . '</lastmod>' . "\n";
    $xml .= '<changefreq>daily</changefreq>' . "\n";
    $xml .= '<priority>1.0</priority>' . "\n";
    $xml .= '</url>' . "\n";
    
    // Pages
    if ( $settings['include_pages'] ) {
        $pages = get_posts( array(
            'post_type' => 'page',
            'post_status' => 'publish',
            'numberposts' => 100
        ) );
        
        foreach ( $pages as $page ) {
            $xml .= '<url>' . "\n";
            $xml .= '<loc>' . esc_url( get_permalink( $page->ID ) ) . '</loc>' . "\n";
            $xml .= '<lastmod>' . date( 'c', strtotime( $page->post_modified ) ) . '</lastmod>' . "\n";
            $xml .= '<changefreq>weekly</changefreq>' . "\n";
            $xml .= '<priority>0.8</priority>' . "\n";
            $xml .= '</url>' . "\n";
        }
    }
    
    // Articles
    if ( $settings['include_posts'] ) {
        $posts = get_posts( array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => 200
        ) );
        
        foreach ( $posts as $post ) {
            $xml .= '<url>' . "\n";
            $xml .= '<loc>' . esc_url( get_permalink( $post->ID ) ) . '</loc>' . "\n";
            $xml .= '<lastmod>' . date( 'c', strtotime( $post->post_modified ) ) . '</lastmod>' . "\n";
            $xml .= '<changefreq>weekly</changefreq>' . "\n";
            $xml .= '<priority>0.7</priority>' . "\n";
            $xml .= '</url>' . "\n";
        }
    }
    
    // Catégories
    if ( $settings['include_categories'] ) {
        $categories = get_categories( array( 'hide_empty' => true ) );
        foreach ( $categories as $category ) {
            $xml .= '<url>' . "\n";
            $xml .= '<loc>' . esc_url( get_category_link( $category->term_id ) ) . '</loc>' . "\n";
            $xml .= '<lastmod>' . date( 'c' ) . '</lastmod>' . "\n";
            $xml .= '<changefreq>weekly</changefreq>' . "\n";
            $xml .= '<priority>0.5</priority>' . "\n";
            $xml .= '</url>' . "\n";
        }
    }
    
    $xml .= '</urlset>';
    return $xml;
}

function show_emergency_interface() {
    $settings = get_option( 'boss_seo_emergency_settings', array(
        'enabled' => true,
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => true
    ) );
    
    $last_generation = get_option( 'boss_seo_last_sitemap_generation', 'Jamais' );
    $sitemap_url = get_option( 'boss_seo_sitemap_url', '' );
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Boss SEO - Générateur d'urgence</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
            .container { background: white; padding: 20px; border-radius: 5px; max-width: 800px; }
            .button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
            .button:hover { background: #005a87; }
            .success { color: green; font-weight: bold; }
            .error { color: red; font-weight: bold; }
            .info { background: #e7f3ff; padding: 10px; border-radius: 3px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚨 Boss SEO - Générateur d'urgence</h1>
            
            <div class="info">
                <strong>Dernière génération :</strong> <?php echo $last_generation; ?><br>
                <?php if ( $sitemap_url ): ?>
                <strong>URL du sitemap :</strong> <a href="<?php echo $sitemap_url; ?>" target="_blank"><?php echo $sitemap_url; ?></a>
                <?php endif; ?>
            </div>
            
            <h2>Paramètres</h2>
            <form id="settingsForm">
                <label><input type="checkbox" name="include_pages" <?php checked( $settings['include_pages'] ); ?>> Inclure les pages</label><br>
                <label><input type="checkbox" name="include_posts" <?php checked( $settings['include_posts'] ); ?>> Inclure les articles</label><br>
                <label><input type="checkbox" name="include_categories" <?php checked( $settings['include_categories'] ); ?>> Inclure les catégories</label><br>
                <button type="button" class="button" onclick="saveSettings()">Sauvegarder</button>
            </form>
            
            <h2>Actions</h2>
            <button class="button" onclick="generateSitemap()">🚀 Générer le sitemap</button>
            <button class="button" onclick="window.open('<?php echo $sitemap_url; ?>', '_blank')">👁️ Voir le sitemap</button>
            
            <div id="result"></div>
        </div>
        
        <script>
        function generateSitemap() {
            document.getElementById('result').innerHTML = 'Génération en cours...';
            
            fetch('?action=generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="error">❌ Erreur: ' + error.message + '</div>';
                });
        }
        
        function saveSettings() {
            const form = document.getElementById('settingsForm');
            const formData = new FormData(form);
            formData.append('action', 'save_settings');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                });
        }
        </script>
    </body>
    </html>
    <?php
}
?>
