<?php
/**
 * Test de l'enregistrement des routes REST API
 * 
 * Ce script vérifie si les routes sont correctement enregistrées.
 */

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier que l'utilisateur est connecté et a les permissions
if ( ! is_user_logged_in() || ! current_user_can( 'manage_options' ) ) {
    die( 'Accès refusé. Vous devez être connecté en tant qu\'administrateur.' );
}

echo "<h1>Test d'enregistrement des routes REST API Boss SEO</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:3px;}</style>";

// Forcer l'initialisation de l'API REST
do_action( 'rest_api_init' );

// Récupérer le serveur REST
$rest_server = rest_get_server();

echo "<h2>1. Routes enregistrées pour boss-seo/v1</h2>";

// Récupérer toutes les routes
$routes = $rest_server->get_routes();

$boss_seo_routes = array();
foreach ( $routes as $route => $handlers ) {
    if ( strpos( $route, '/boss-seo/v1/' ) === 0 ) {
        $boss_seo_routes[$route] = $handlers;
    }
}

if ( empty( $boss_seo_routes ) ) {
    echo "<span class='error'>❌ Aucune route boss-seo/v1 trouvée</span><br>";
} else {
    echo "<span class='success'>✅ " . count( $boss_seo_routes ) . " routes boss-seo/v1 trouvées</span><br>";
    
    echo "<h3>Routes liées aux sitemaps:</h3>";
    foreach ( $boss_seo_routes as $route => $handlers ) {
        if ( strpos( $route, 'robots-sitemap' ) !== false ) {
            echo "<strong>{$route}</strong><br>";
            foreach ( $handlers as $handler ) {
                $methods = isset( $handler['methods'] ) ? $handler['methods'] : 'N/A';
                if ( is_array( $methods ) ) {
                    $methods = implode( ', ', $methods );
                }
                echo "&nbsp;&nbsp;- Méthodes: {$methods}<br>";
                
                if ( isset( $handler['callback'] ) && is_array( $handler['callback'] ) ) {
                    $class = is_object( $handler['callback'][0] ) ? get_class( $handler['callback'][0] ) : $handler['callback'][0];
                    $method = $handler['callback'][1];
                    echo "&nbsp;&nbsp;- Callback: {$class}::{$method}<br>";
                }
            }
            echo "<br>";
        }
    }
}

echo "<h2>2. Test spécifique de la route de régénération</h2>";

$regenerate_route = '/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate';
if ( isset( $boss_seo_routes[$regenerate_route] ) ) {
    echo "<span class='success'>✅ Route de régénération trouvée: {$regenerate_route}</span><br>";
    
    $handler = $boss_seo_routes[$regenerate_route][0];
    if ( isset( $handler['callback'] ) && is_array( $handler['callback'] ) ) {
        $class = is_object( $handler['callback'][0] ) ? get_class( $handler['callback'][0] ) : $handler['callback'][0];
        $method = $handler['callback'][1];
        echo "<span class='info'>Callback: {$class}::{$method}</span><br>";
        
        // Vérifier si la méthode existe
        if ( is_object( $handler['callback'][0] ) && method_exists( $handler['callback'][0], $method ) ) {
            echo "<span class='success'>✅ Méthode {$method} existe dans la classe</span><br>";
        } else {
            echo "<span class='error'>❌ Méthode {$method} n'existe pas dans la classe</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ Route de régénération non trouvée: {$regenerate_route}</span><br>";
    
    // Chercher des routes similaires
    echo "<h3>Routes similaires trouvées:</h3>";
    foreach ( $boss_seo_routes as $route => $handlers ) {
        if ( strpos( $route, 'regenerate' ) !== false ) {
            echo "<span class='info'>- {$route}</span><br>";
        }
    }
}

echo "<h2>3. Test de la classe contrôleur</h2>";

try {
    if ( ! class_exists( 'Boss_Robots_Sitemap_Controller' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/technical/class-boss-robots-sitemap-controller.php' );
    }
    
    $controller = new Boss_Robots_Sitemap_Controller( 'boss-seo', '1.2.0' );
    echo "<span class='success'>✅ Contrôleur instancié avec succès</span><br>";
    
    // Vérifier si la méthode existe
    if ( method_exists( $controller, 'regenerate_advanced_sitemap' ) ) {
        echo "<span class='success'>✅ Méthode regenerate_advanced_sitemap existe</span><br>";
    } else {
        echo "<span class='error'>❌ Méthode regenerate_advanced_sitemap n'existe pas</span><br>";
    }
    
    // Vérifier les permissions
    if ( method_exists( $controller, 'check_permissions' ) ) {
        $has_permissions = $controller->check_permissions();
        echo "<span class='" . ( $has_permissions ? 'success' : 'error' ) . "'>" . 
             ( $has_permissions ? '✅' : '❌' ) . " Permissions: " . 
             ( $has_permissions ? 'OK' : 'Insuffisantes' ) . "</span><br>";
    }
    
} catch ( Exception $e ) {
    echo "<span class='error'>❌ Erreur contrôleur: " . $e->getMessage() . "</span><br>";
}

echo "<h2>4. Test de l'enregistrement manuel</h2>";

try {
    // Tenter d'enregistrer manuellement les routes
    if ( isset( $controller ) ) {
        echo "<span class='info'>Tentative d'enregistrement manuel des routes...</span><br>";
        $controller->register_rest_routes();
        echo "<span class='success'>✅ Enregistrement manuel réussi</span><br>";
        
        // Re-vérifier les routes
        $routes_after = $rest_server->get_routes();
        if ( isset( $routes_after[$regenerate_route] ) ) {
            echo "<span class='success'>✅ Route de régénération maintenant disponible</span><br>";
        } else {
            echo "<span class='error'>❌ Route de régénération toujours non disponible</span><br>";
        }
    }
} catch ( Exception $e ) {
    echo "<span class='error'>❌ Erreur enregistrement manuel: " . $e->getMessage() . "</span><br>";
}

echo "<h2>5. Informations de débogage</h2>";
echo "WordPress Version: " . get_bloginfo( 'version' ) . "<br>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current User ID: " . get_current_user_id() . "<br>";
echo "Current User Can Manage Options: " . ( current_user_can( 'manage_options' ) ? 'Oui' : 'Non' ) . "<br>";
echo "REST API Enabled: " . ( function_exists( 'rest_get_server' ) ? 'Oui' : 'Non' ) . "<br>";

?>
