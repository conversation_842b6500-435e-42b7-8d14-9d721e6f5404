<?php
/**
 * GÉNÉRATEUR DE SITEMAP ULTRA-ROBUSTE avec toutes les dépendances
 *
 * Installation : composer install
 * Accès : /wp-content/plugins/Bossseov1.1/boss-seo-ultra-robust-sitemap.php?action=generate
 */

// Désactiver l'affichage des erreurs pour éviter les problèmes JSON
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// Headers JSON en premier pour les requêtes AJAX
if (isset($_GET['action'])) {
    header('Content-Type: application/json; charset=UTF-8');
    header('Cache-Control: no-cache, must-revalidate');
}

// Nettoyer le buffer
if (ob_get_level()) {
    ob_end_clean();
}

// Inclure WordPress de manière sécurisée
$wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
if (!file_exists($wp_load_path)) {
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => 'WordPress non trouvé',
            'version' => 'ultra-robust'
        ));
        exit;
    }
    die('WordPress non trouvé');
}

require_once($wp_load_path);

// Vérifier si Composer est installé
$has_composer = file_exists(__DIR__ . '/vendor/autoload.php');
$has_samdark = file_exists(__DIR__ . '/vendor/samdark/sitemap');

if (!$has_composer || !$has_samdark) {
    // Si c'est une requête AJAX, retourner du JSON
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Dépendances Composer manquantes pour la version ultra-robuste',
            'version' => 'ultra-robust',
            'install_command' => 'composer install',
            'composer_exists' => $has_composer,
            'samdark_exists' => $has_samdark
        ));
        exit;
    }

    // Sinon afficher l'interface HTML
    wp_die( '
        <h1>📦 Installation Ultra-Robuste Requise</h1>
        <p>Veuillez installer les dépendances Composer :</p>
        <pre>cd ' . __DIR__ . ' && composer install</pre>
        <p><strong>Statut :</strong></p>
        <ul>
            <li>Composer autoload: ' . ($has_composer ? '✅' : '❌') . '</li>
            <li>samdark/sitemap: ' . ($has_samdark ? '✅' : '❌') . '</li>
        </ul>
        <p>Ou utilisez la <a href="boss-seo-professional-sitemap.php">solution professionnelle</a> ou <a href="boss-seo-emergency-sitemap.php">solution d\'urgence</a></p>
    ' );
}

// Charger Composer de manière sécurisée
try {
    require_once __DIR__ . '/vendor/autoload.php';

    // Vérifier que les classes sont disponibles
    if (!class_exists('samdark\sitemap\Sitemap')) {
        throw new Exception('Classe samdark\sitemap\Sitemap non trouvée');
    }

} catch (Exception $e) {
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur chargement Composer: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
        exit;
    }
    wp_die('Erreur chargement Composer: ' . $e->getMessage());
}

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    if (isset($_GET['action'])) {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => false,
            'message' => 'Accès refusé',
            'version' => 'ultra-robust'
        ));
        exit;
    }
    wp_die( 'Accès refusé' );
}

// Initialiser le logger de manière sécurisée
$logger = null;
try {
    if (class_exists('Monolog\Logger')) {
        $logger = new Monolog\Logger('boss-seo-sitemap');
        $log_dir = __DIR__ . '/logs/';
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }
        $logger->pushHandler(new Monolog\Handler\StreamHandler($log_dir . 'sitemap.log', Monolog\Logger::INFO));
    }
} catch (Exception $e) {
    // Logger optionnel, continuer sans
}

$action = isset( $_GET['action'] ) ? $_GET['action'] : '';

try {
    switch ( $action ) {
        case 'generate':
            generate_ultra_robust_sitemap();
            break;
        case 'ping_engines':
            ping_search_engines();
            break;
        case 'export_csv':
            export_sitemap_csv();
            break;
        case 'analytics':
            get_sitemap_analytics();
            break;
        case 'save_settings':
            save_ultra_robust_settings();
            break;
        case 'get_settings':
            get_ultra_robust_settings();
            break;
        default:
            if (isset($_GET['action'])) {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'Action non reconnue: ' . $action,
                    'version' => 'ultra-robust'
                ));
            } else {
                show_ultra_robust_interface();
            }
            break;
    }
} catch (Exception $e) {
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur ultra-robuste: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    } else {
        wp_die('Erreur: ' . $e->getMessage());
    }
} catch (Error $e) {
    if (isset($_GET['action'])) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur fatale: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    } else {
        wp_die('Erreur fatale: ' . $e->getMessage());
    }
}

function generate_ultra_robust_sitemap() {
    global $logger;
    
    try {
        $logger->info('🚀 Début génération ultra-robuste des sitemaps');
        
        $upload_dir = wp_upload_dir();
        $sitemap_dir = $upload_dir['basedir'] . '/boss-seo-ultra-sitemaps/';
        
        // Créer le système de fichiers
        $adapter = new LocalFilesystemAdapter($sitemap_dir);
        $filesystem = new Filesystem($adapter);
        
        // Créer le dossier s'il n'existe pas
        if ( ! file_exists( $sitemap_dir ) ) {
            wp_mkdir_p( $sitemap_dir );
            wp_mkdir_p( $sitemap_dir . 'logs/' );
        }
        
        $settings = get_option( 'boss_seo_ultra_robust_settings', array(
            'include_posts' => true,
            'include_pages' => true,
            'include_categories' => true,
            'include_tags' => false,
            'include_images' => true,
            'max_urls_per_sitemap' => 50000,
            'enable_compression' => true,
            'enable_analytics' => true,
            'ping_engines' => true
        ) );
        
        $generation_id = Uuid::uuid4()->toString();
        $start_time = Carbon::now();
        
        $logger->info("📊 Génération ID: {$generation_id}");
        
        // Créer l'index des sitemaps avec les deux librairies
        $index_samdark = new Index( $sitemap_dir . 'sitemap-index.xml' );
        
        $urlset_pixel = new Urlset();
        
        $total_urls = 0;
        $sitemaps_created = array();
        $analytics_data = array();
        
        // === SITEMAP PRINCIPAL ===
        $main_sitemap = new Sitemap( $sitemap_dir . 'sitemap-main.xml' );
        
        // Page d'accueil avec métadonnées enrichies
        $home_url = home_url( '/' );
        $main_sitemap->addItem( $home_url, time(), Sitemap::DAILY, 1.0 );
        
        // Ajouter avec PixelDeveloper pour plus d'options
        $home_url_obj = new Url($home_url);
        $home_url_obj->setLastMod(Carbon::now());
        $home_url_obj->setChangeFreq('daily');
        $home_url_obj->setPriority(1.0);
        $urlset_pixel->addUrl($home_url_obj);
        
        $total_urls++;
        $logger->info("✅ Page d'accueil ajoutée: {$home_url}");
        
        // === PAGES ===
        if ( $settings['include_pages'] ) {
            $pages = get_posts( array(
                'post_type' => 'page',
                'post_status' => 'publish',
                'numberposts' => $settings['max_urls_per_sitemap']
            ) );
            
            foreach ( $pages as $page ) {
                $page_url = get_permalink( $page->ID );
                $last_mod = strtotime( $page->post_modified );
                
                $main_sitemap->addItem( $page_url, $last_mod, Sitemap::WEEKLY, 0.8 );
                
                // Analytics : collecter des métadonnées
                $analytics_data['pages'][] = array(
                    'id' => $page->ID,
                    'url' => $page_url,
                    'title' => $page->post_title,
                    'last_modified' => $page->post_modified,
                    'word_count' => str_word_count( strip_tags( $page->post_content ) )
                );
                
                $total_urls++;
            }
            
            $logger->info("📄 " . count($pages) . " pages ajoutées");
        }
        
        // === ARTICLES ===
        if ( $settings['include_posts'] ) {
            $posts = get_posts( array(
                'post_type' => 'post',
                'post_status' => 'publish',
                'numberposts' => $settings['max_urls_per_sitemap']
            ) );
            
            foreach ( $posts as $post ) {
                $post_url = get_permalink( $post->ID );
                $last_mod = strtotime( $post->post_modified );
                
                // Priorité dynamique basée sur la date
                $days_old = (time() - strtotime($post->post_date)) / (60 * 60 * 24);
                $priority = $days_old < 30 ? 0.9 : ($days_old < 90 ? 0.7 : 0.5);
                
                $main_sitemap->addItem( $post_url, $last_mod, Sitemap::WEEKLY, $priority );
                
                // Analytics
                $analytics_data['posts'][] = array(
                    'id' => $post->ID,
                    'url' => $post_url,
                    'title' => $post->post_title,
                    'last_modified' => $post->post_modified,
                    'priority' => $priority,
                    'days_old' => round($days_old)
                );
                
                $total_urls++;
            }
            
            $logger->info("📝 " . count($posts) . " articles ajoutés");
        }
        
        $main_sitemap->write();
        $index_samdark->addSitemap( $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/sitemap-main.xml' );
        $sitemaps_created[] = 'sitemap-main.xml';
        
        // === SITEMAP IMAGES ===
        if ( $settings['include_images'] ) {
            $images_sitemap = new Sitemap( $sitemap_dir . 'sitemap-images.xml' );
            
            $attachments = get_posts( array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_status' => 'inherit',
                'numberposts' => 1000
            ) );
            
            foreach ( $attachments as $attachment ) {
                $image_url = wp_get_attachment_url( $attachment->ID );
                if ( $image_url ) {
                    $images_sitemap->addItem( $image_url, strtotime( $attachment->post_modified ), Sitemap::MONTHLY, 0.3 );
                    $total_urls++;
                }
            }
            
            $images_sitemap->write();
            $index_samdark->addSitemap( $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/sitemap-images.xml' );
            $sitemaps_created[] = 'sitemap-images.xml';
            
            $logger->info("🖼️ " . count($attachments) . " images ajoutées");
        }
        
        // Écrire l'index
        $index_samdark->write();
        
        $end_time = Carbon::now();
        $generation_time = $end_time->diffInSeconds($start_time);
        
        // Sauvegarder les analytics
        if ( $settings['enable_analytics'] ) {
            $analytics_summary = array(
                'generation_id' => $generation_id,
                'start_time' => $start_time->toISOString(),
                'end_time' => $end_time->toISOString(),
                'generation_time_seconds' => $generation_time,
                'total_urls' => $total_urls,
                'sitemaps_created' => $sitemaps_created,
                'data' => $analytics_data
            );
            
            file_put_contents( $sitemap_dir . 'analytics.json', json_encode( $analytics_summary, JSON_PRETTY_PRINT ) );
        }
        
        // Ping automatique des moteurs
        if ( $settings['ping_engines'] ) {
            ping_search_engines_async();
        }
        
        // Mettre à jour les options
        update_option( 'boss_seo_last_sitemap_generation', current_time( 'mysql' ) );
        update_option( 'boss_seo_sitemap_index_url', $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/sitemap-index.xml' );
        
        $logger->info("🎉 Génération terminée en {$generation_time}s - {$total_urls} URLs");
        
        // Réponse JSON
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => true,
            'message' => "Sitemaps ultra-robustes générés avec succès - {$total_urls} URLs en {$generation_time}s",
            'sitemap_index_url' => $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/sitemap-index.xml',
            'total_urls' => $total_urls,
            'generation_time' => $generation_time,
            'generation_id' => $generation_id,
            'sitemaps_created' => $sitemaps_created,
            'analytics_url' => $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/analytics.json'
        ) );
        
    } catch ( Exception $e ) {
        $logger->error('❌ Erreur génération: ' . $e->getMessage());
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        ) );
    }
    exit;
}

function ping_search_engines_async() {
    global $logger;
    
    try {
        $client = new Client(['timeout' => 10]);
        $sitemap_url = get_option( 'boss_seo_sitemap_index_url' );
        
        if ( ! $sitemap_url ) {
            return;
        }
        
        // Ping Google
        $google_url = 'https://www.google.com/ping?sitemap=' . urlencode( $sitemap_url );
        $client->getAsync( $google_url );
        
        // Ping Bing
        $bing_url = 'https://www.bing.com/ping?sitemap=' . urlencode( $sitemap_url );
        $client->getAsync( $bing_url );
        
        $logger->info("📤 Ping moteurs envoyé pour: {$sitemap_url}");
        
    } catch ( Exception $e ) {
        $logger->error('❌ Erreur ping moteurs: ' . $e->getMessage());
    }
}

function get_ultra_robust_settings() {
    // Nettoyer le buffer
    if (ob_get_level()) {
        ob_clean();
    }

    $default_settings = array(
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => true,
        'include_tags' => false,
        'include_images' => true,
        'enable_analytics' => true,
        'enable_logs' => true,
        'max_urls_per_sitemap' => 50000,
        'enable_compression' => true,
        'ping_engines' => true
    );

    // Récupérer les paramètres de manière sécurisée
    $settings = $default_settings;
    if (function_exists('get_option')) {
        $saved_settings = get_option('boss_seo_ultra_robust_settings', array());
        if (is_array($saved_settings)) {
            $settings = array_merge($default_settings, $saved_settings);
        }
    }

    echo json_encode(array(
        'success' => true,
        'settings' => $settings,
        'version' => 'ultra-robust',
        'composer_installed' => file_exists(__DIR__ . '/vendor/autoload.php'),
        'samdark_available' => file_exists(__DIR__ . '/vendor/samdark/sitemap'),
        'features' => array(
            'analytics' => true,
            'csv_export' => true,
            'advanced_ping' => true,
            'detailed_logs' => true,
            'image_sitemaps' => true,
            'multi_format' => true
        )
    ), JSON_UNESCAPED_SLASHES);

    exit;
}

function save_ultra_robust_settings() {
    try {
        $settings = array(
            'include_posts' => isset($_POST['include_posts']),
            'include_pages' => isset($_POST['include_pages']),
            'include_categories' => isset($_POST['include_categories']),
            'include_tags' => isset($_POST['include_tags']),
            'include_images' => isset($_POST['include_images']),
            'enable_analytics' => isset($_POST['enable_analytics']),
            'enable_logs' => isset($_POST['enable_logs']),
            'enable_compression' => isset($_POST['enable_compression']),
            'ping_engines' => isset($_POST['ping_engines']),
            'max_urls_per_sitemap' => intval($_POST['max_urls_per_sitemap'] ?? 50000),
            'last_updated' => current_time('mysql')
        );

        update_option('boss_seo_ultra_robust_settings', $settings);

        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => true,
            'message' => 'Paramètres ultra-robustes sauvegardés',
            'settings' => $settings,
            'version' => 'ultra-robust'
        ), JSON_UNESCAPED_SLASHES);

    } catch (Exception $e) {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur sauvegarde: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    }
    exit;
}

function get_sitemap_analytics() {
    try {
        $upload_dir = wp_upload_dir();
        $analytics_file = $upload_dir['basedir'] . '/boss-seo-ultra-sitemaps/analytics.json';

        if (file_exists($analytics_file)) {
            $analytics = json_decode(file_get_contents($analytics_file), true);
        } else {
            $analytics = array('message' => 'Aucune donnée disponible');
        }

        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => true,
            'analytics' => $analytics,
            'version' => 'ultra-robust'
        ), JSON_UNESCAPED_SLASHES);

    } catch (Exception $e) {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur analytics: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    }
    exit;
}

function export_sitemap_csv() {
    try {
        $upload_dir = wp_upload_dir();
        $analytics_file = $upload_dir['basedir'] . '/boss-seo-ultra-sitemaps/analytics.json';

        if (!file_exists($analytics_file)) {
            throw new Exception('Aucune donnée à exporter');
        }

        $analytics = json_decode(file_get_contents($analytics_file), true);
        $csv_file = $upload_dir['basedir'] . '/boss-seo-ultra-sitemaps/export-' . date('Y-m-d-H-i-s') . '.csv';

        $csv_content = "Type,URL,Title,Last Modified,Priority\n";

        if (isset($analytics['data']['pages'])) {
            foreach ($analytics['data']['pages'] as $page) {
                $csv_content .= "Page,{$page['url']},{$page['title']},{$page['last_modified']},0.8\n";
            }
        }

        if (isset($analytics['data']['posts'])) {
            foreach ($analytics['data']['posts'] as $post) {
                $csv_content .= "Post,{$post['url']},{$post['title']},{$post['last_modified']},{$post['priority']}\n";
            }
        }

        file_put_contents($csv_file, $csv_content);

        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => true,
            'download_url' => $upload_dir['baseurl'] . '/boss-seo-ultra-sitemaps/' . basename($csv_file),
            'file_size' => strlen($csv_content),
            'version' => 'ultra-robust'
        ), JSON_UNESCAPED_SLASHES);

    } catch (Exception $e) {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur export: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    }
    exit;
}

function ping_search_engines() {
    try {
        $sitemap_url = get_option('boss_seo_sitemap_index_url');

        if (!$sitemap_url) {
            throw new Exception('URL du sitemap non trouvée');
        }

        $results = array();

        // Ping Google
        $google_url = 'https://www.google.com/ping?sitemap=' . urlencode($sitemap_url);
        $google_response = wp_remote_get($google_url, array('timeout' => 10));
        $results['google'] = array(
            'success' => !is_wp_error($google_response) && wp_remote_retrieve_response_code($google_response) == 200,
            'message' => is_wp_error($google_response) ? $google_response->get_error_message() : 'Ping envoyé'
        );

        // Ping Bing
        $bing_url = 'https://www.bing.com/ping?sitemap=' . urlencode($sitemap_url);
        $bing_response = wp_remote_get($bing_url, array('timeout' => 10));
        $results['bing'] = array(
            'success' => !is_wp_error($bing_response) && wp_remote_retrieve_response_code($bing_response) == 200,
            'message' => is_wp_error($bing_response) ? $bing_response->get_error_message() : 'Ping envoyé'
        );

        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => true,
            'results' => $results,
            'sitemap_url' => $sitemap_url,
            'version' => 'ultra-robust'
        ), JSON_UNESCAPED_SLASHES);

    } catch (Exception $e) {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(array(
            'success' => false,
            'message' => 'Erreur ping: ' . $e->getMessage(),
            'version' => 'ultra-robust'
        ));
    }
    exit;
}
?>
