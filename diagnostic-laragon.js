/**
 * Diagnostic spécifique pour environnement Laragon
 */

console.log('🔍 Diagnostic Environnement Laragon');
console.log('==================================');

// Vérification de l'environnement
function checkEnvironment() {
    console.log('\n🌐 === ENVIRONNEMENT ===');
    
    console.log(`🔗 URL actuelle: ${window.location.href}`);
    console.log(`🌍 Protocole: ${window.location.protocol}`);
    console.log(`🏠 Host: ${window.location.host}`);
    console.log(`📁 Path: ${window.location.pathname}`);
    
    // Vérifier si c'est bien un environnement local
    const isLocal = window.location.hostname === 'localhost' || 
                   window.location.hostname.includes('.test') ||
                   window.location.hostname.includes('.local') ||
                   window.location.hostname.startsWith('192.168.') ||
                   window.location.hostname.startsWith('127.0.0.1');
    
    console.log(`🏠 Environnement local: ${isLocal ? '✅ OUI' : '❌ NON'}`);
    
    // Vérifier HTTPS
    const isHTTPS = window.location.protocol === 'https:';
    console.log(`🔒 HTTPS: ${isHTTPS ? '✅ OUI' : '⚠️ NON (normal en local)'}`);
    
    return { isLocal, isHTTPS };
}

// Vérification de la configuration WordPress
function checkWordPressConfig() {
    console.log('\n⚙️ === CONFIGURATION WORDPRESS ===');
    
    // Vérifier les variables globales
    const checks = [
        { name: 'wpApiSettings', exists: typeof wpApiSettings !== 'undefined' },
        { name: 'wpApiSettings.root', exists: wpApiSettings && wpApiSettings.root },
        { name: 'wpApiSettings.nonce', exists: wpApiSettings && wpApiSettings.nonce },
        { name: 'wp.apiFetch', exists: typeof wp !== 'undefined' && wp.apiFetch }
    ];
    
    checks.forEach(check => {
        console.log(`${check.exists ? '✅' : '❌'} ${check.name}: ${check.exists ? 'OK' : 'MANQUANT'}`);
    });
    
    if (wpApiSettings) {
        console.log(`🔑 Nonce: ${wpApiSettings.nonce ? wpApiSettings.nonce.substring(0, 10) + '...' : 'MANQUANT'}`);
        console.log(`🌐 API Root: ${wpApiSettings.root || 'NON DÉFINI'}`);
    }
    
    return checks.every(check => check.exists);
}

// Test de connectivité API de base
async function testBasicConnectivity() {
    console.log('\n📡 === CONNECTIVITÉ API ===');
    
    const tests = [
        { url: '/wp-json/', name: 'WordPress REST API' },
        { url: '/wp-json/wp/v2/', name: 'WordPress Core API' },
        { url: '/wp-json/boss-seo/v1/', name: 'Boss SEO API Base' }
    ];
    
    const results = {};
    
    for (const test of tests) {
        console.log(`🔍 Test: ${test.name}`);
        
        try {
            const response = await fetch(test.url, {
                method: 'GET',
                headers: wpApiSettings?.nonce ? { 'X-WP-Nonce': wpApiSettings.nonce } : {}
            });
            
            const success = response.ok;
            results[test.name] = success;
            
            console.log(`  📊 Status: ${response.status}`);
            console.log(`  ${success ? '✅' : '❌'} ${test.name}: ${success ? 'OK' : 'ERREUR'}`);
            
            if (!success) {
                const text = await response.text();
                console.log(`  📄 Erreur: ${text.substring(0, 100)}...`);
            }
            
        } catch (error) {
            results[test.name] = false;
            console.log(`  💥 Erreur réseau: ${error.message}`);
        }
    }
    
    return results;
}

// Test spécifique des APIs Boss SEO problématiques
async function testBossSEOAPIs() {
    console.log('\n🔧 === APIS BOSS SEO ===');
    
    const apis = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
            method: 'POST',
            data: { test: true },
            name: 'Sauvegarde Paramètres'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
            method: 'POST',
            name: 'Régénération Sitemap'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            method: 'GET',
            name: 'Dashboard Stats'
        }
    ];
    
    const results = {};
    
    for (const api of apis) {
        console.log(`\n🔍 Test: ${api.name}`);
        
        try {
            const options = {
                method: api.method,
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            };
            
            if (api.data && api.method !== 'GET') {
                options.headers['Content-Type'] = 'application/json';
                options.body = JSON.stringify(api.data);
            }
            
            const response = await fetch(api.url, options);
            
            console.log(`  📊 Status: ${response.status}`);
            console.log(`  📋 Headers:`, Object.fromEntries(response.headers.entries()));
            
            const text = await response.text();
            console.log(`  📄 Réponse (${text.length} chars): ${text.substring(0, 150)}...`);
            
            if (response.ok) {
                try {
                    const json = JSON.parse(text);
                    console.log(`  ✅ ${api.name}: JSON valide`);
                    results[api.name] = true;
                } catch (e) {
                    console.log(`  ⚠️ ${api.name}: JSON invalide`);
                    results[api.name] = false;
                }
            } else {
                console.log(`  ❌ ${api.name}: Erreur HTTP ${response.status}`);
                results[api.name] = false;
                
                // Analyser les erreurs PHP courantes
                if (text.includes('Fatal error')) {
                    console.log('  🚨 ERREUR FATALE PHP DÉTECTÉE');
                    const match = text.match(/Fatal error: (.*?) in/);
                    if (match) {
                        console.log(`    💥 Erreur: ${match[1]}`);
                    }
                }
                
                if (text.includes('Parse error')) {
                    console.log('  🚨 ERREUR DE SYNTAXE PHP DÉTECTÉE');
                }
                
                if (text.includes('Call to undefined')) {
                    console.log('  🚨 FONCTION/MÉTHODE INEXISTANTE DÉTECTÉE');
                    const match = text.match(/Call to undefined (.*?) in/);
                    if (match) {
                        console.log(`    💥 Fonction: ${match[1]}`);
                    }
                }
            }
            
        } catch (error) {
            console.log(`  💥 Erreur réseau: ${error.message}`);
            results[api.name] = false;
        }
        
        // Pause entre les tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return results;
}

// Recommandations spécifiques Laragon
function generateLaragonRecommendations(envCheck, wpCheck, connectivityResults, apiResults) {
    console.log('\n💡 === RECOMMANDATIONS LARAGON ===');
    
    const issues = [];
    const solutions = [];
    
    // Analyser les problèmes
    if (!wpCheck) {
        issues.push('Configuration WordPress incomplète');
        solutions.push('🔧 Vérifier que WordPress est correctement installé');
        solutions.push('🔧 Vérifier les permissions de fichiers');
    }
    
    const connectivityOK = Object.values(connectivityResults).every(r => r);
    if (!connectivityOK) {
        issues.push('Problèmes de connectivité API');
        solutions.push('🔧 Vérifier la configuration Apache/Nginx dans Laragon');
        solutions.push('🔧 Vérifier les règles de réécriture (.htaccess)');
        solutions.push('🔧 Redémarrer les services Laragon');
    }
    
    const apiOK = Object.values(apiResults).some(r => r);
    if (!apiOK) {
        issues.push('Toutes les APIs Boss SEO échouent');
        solutions.push('🔧 Vérifier que le plugin Boss SEO est activé');
        solutions.push('🔧 Vérifier les logs PHP de Laragon');
        solutions.push('🔧 Augmenter memory_limit dans php.ini');
        solutions.push('🔧 Activer error_reporting dans php.ini');
    }
    
    // Recommandations spécifiques Laragon
    console.log('🔧 ACTIONS RECOMMANDÉES POUR LARAGON:');
    console.log('');
    console.log('1. 📁 Vérifier les logs PHP:');
    console.log('   - Laragon > Menu > PHP > Error Logs');
    console.log('   - Ou: C:\\laragon\\bin\\php\\php-X.X.X\\logs\\');
    console.log('');
    console.log('2. ⚙️ Configuration PHP recommandée:');
    console.log('   - memory_limit = 256M');
    console.log('   - max_execution_time = 300');
    console.log('   - error_reporting = E_ALL');
    console.log('   - display_errors = On');
    console.log('');
    console.log('3. 🔄 Redémarrer les services:');
    console.log('   - Laragon > Stop All');
    console.log('   - Laragon > Start All');
    console.log('');
    console.log('4. 🔍 Vérifier WordPress:');
    console.log('   - wp-config.php: WP_DEBUG = true');
    console.log('   - Permaliens: Réglages > Permaliens > Enregistrer');
    console.log('');
    
    if (solutions.length > 0) {
        console.log('🎯 SOLUTIONS SPÉCIFIQUES:');
        solutions.forEach(solution => console.log(`   ${solution}`));
    }
    
    return { issues, solutions };
}

// Diagnostic complet
async function runLaragonDiagnostic() {
    console.log('🚀 Démarrage diagnostic Laragon...\n');
    
    try {
        // 1. Vérifier l'environnement
        const envCheck = checkEnvironment();
        
        // 2. Vérifier WordPress
        const wpCheck = checkWordPressConfig();
        
        // 3. Tester la connectivité
        const connectivityResults = await testBasicConnectivity();
        
        // 4. Tester les APIs Boss SEO
        const apiResults = await testBossSEOAPIs();
        
        // 5. Générer les recommandations
        const recommendations = generateLaragonRecommendations(envCheck, wpCheck, connectivityResults, apiResults);
        
        // Rapport final
        console.log('\n📊 === RAPPORT DIAGNOSTIC LARAGON ===');
        console.log(`🌐 Environnement local: ${envCheck.isLocal ? '✅' : '❌'}`);
        console.log(`⚙️ WordPress configuré: ${wpCheck ? '✅' : '❌'}`);
        
        const connectivityOK = Object.values(connectivityResults).filter(r => r).length;
        console.log(`📡 APIs de base: ${connectivityOK}/${Object.keys(connectivityResults).length} OK`);
        
        const apiOK = Object.values(apiResults).filter(r => r).length;
        console.log(`🔧 APIs Boss SEO: ${apiOK}/${Object.keys(apiResults).length} OK`);
        
        if (apiOK === Object.keys(apiResults).length) {
            console.log('\n🎉 DIAGNOSTIC LARAGON: TOUT FONCTIONNE !');
        } else {
            console.log('\n⚠️ DIAGNOSTIC LARAGON: PROBLÈMES DÉTECTÉS');
            console.log('📋 Consultez les recommandations ci-dessus');
        }
        
        return {
            environment: envCheck,
            wordpress: wpCheck,
            connectivity: connectivityResults,
            apis: apiResults,
            recommendations: recommendations
        };
        
    } catch (error) {
        console.log('\n💥 Erreur lors du diagnostic:', error);
        return { error: error.message };
    }
}

// Rendre disponible globalement
window.diagnosticLaragon = runLaragonDiagnostic;

console.log('\n🛠️ Diagnostic Laragon disponible:');
console.log('- diagnosticLaragon() - Diagnostic complet pour Laragon');
console.log('\n🎯 Pour commencer, tapez: diagnosticLaragon()');

// Auto-exécution
console.log('\n🚀 Exécution automatique...\n');
runLaragonDiagnostic();
