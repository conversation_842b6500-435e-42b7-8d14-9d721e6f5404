<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit38fff39e60531b7492fcc7f770a644cc
{
    public static $prefixLengthsPsr4 = array (
        's' => 
        array (
            'samdark\\sitemap\\' => 16,
        ),
        'B' => 
        array (
            'Boss<PERSON><PERSON>\\Sitemap\\' => 16,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'samdark\\sitemap\\' => 
        array (
            0 => __DIR__ . '/..' . '/samdark/sitemap',
        ),
        'BossSEO\\Sitemap\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src/Sitemap',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'samdark\\sitemap\\DeflateWriter' => __DIR__ . '/..' . '/samdark/sitemap/DeflateWriter.php',
        'samdark\\sitemap\\Index' => __DIR__ . '/..' . '/samdark/sitemap/Index.php',
        'samdark\\sitemap\\PlainFileWriter' => __DIR__ . '/..' . '/samdark/sitemap/PlainFileWriter.php',
        'samdark\\sitemap\\Sitemap' => __DIR__ . '/..' . '/samdark/sitemap/Sitemap.php',
        'samdark\\sitemap\\TempFileGZIPWriter' => __DIR__ . '/..' . '/samdark/sitemap/TempFileGZIPWriter.php',
        'samdark\\sitemap\\WriterInterface' => __DIR__ . '/..' . '/samdark/sitemap/WriterInterface.php',
        'samdark\\sitemap\\tests\\IndexTest' => __DIR__ . '/..' . '/samdark/sitemap/tests/IndexTest.php',
        'samdark\\sitemap\\tests\\SitemapTest' => __DIR__ . '/..' . '/samdark/sitemap/tests/SitemapTest.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit38fff39e60531b7492fcc7f770a644cc::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit38fff39e60531b7492fcc7f770a644cc::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit38fff39e60531b7492fcc7f770a644cc::$classMap;

        }, null, ClassLoader::class);
    }
}
