/**
 * Test des fonctionnalités restaurées Boss SEO
 */

console.log('🔄 Test des Fonctionnalités Restaurées');
console.log('=====================================');

// Test de la sauvegarde avec données réelles
async function testRealSave() {
    console.log('\n💾 Test Sauvegarde avec Données Réelles...');
    
    const testSettings = {
        enabled: true,
        includeImages: true,
        includeLastMod: true,
        defaultChangeFreq: 'weekly',
        defaultPriority: 0.8,
        includedPostTypes: ['post', 'page']
    };
    
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testSettings)
        });
        
        console.log(`📊 Status: ${response.status}`);
        
        const text = await response.text();
        console.log(`📄 Réponse brute: ${text.substring(0, 200)}...`);
        
        if (response.ok) {
            try {
                const json = JSON.parse(text);
                console.log('✅ JSON valide:', json);
                
                if (json.success) {
                    console.log('🎉 Sauvegarde réussie !');
                    console.log(`📝 Message: ${json.message}`);
                    if (json.data) {
                        console.log('📊 Données sauvegardées:', json.data);
                    }
                    return true;
                } else {
                    console.log('⚠️ Sauvegarde échouée:', json.message);
                    return false;
                }
            } catch (e) {
                console.log('❌ JSON invalide:', e.message);
                return false;
            }
        } else {
            console.log('❌ Erreur HTTP:', response.status);
            return false;
        }
    } catch (error) {
        console.log('💥 Erreur réseau:', error);
        return false;
    }
}

// Test de la régénération avec données réelles
async function testRealRegen() {
    console.log('\n🔄 Test Régénération avec Données Réelles...');
    
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        
        console.log(`📊 Status: ${response.status}`);
        
        const text = await response.text();
        console.log(`📄 Réponse brute: ${text.substring(0, 200)}...`);
        
        if (response.ok) {
            try {
                const json = JSON.parse(text);
                console.log('✅ JSON valide:', json);
                
                if (json.success) {
                    console.log('🎉 Régénération réussie !');
                    console.log(`📝 Message: ${json.message}`);
                    if (json.data) {
                        console.log('📊 Résultats:', json.data);
                        console.log(`📈 Sitemaps régénérés: ${json.data.regenerated}/${json.data.total}`);
                    }
                    return true;
                } else {
                    console.log('⚠️ Régénération échouée:', json.message);
                    return false;
                }
            } catch (e) {
                console.log('❌ JSON invalide:', e.message);
                return false;
            }
        } else {
            console.log('❌ Erreur HTTP:', response.status);
            return false;
        }
    } catch (error) {
        console.log('💥 Erreur réseau:', error);
        return false;
    }
}

// Test des autres APIs existantes
async function testOtherAPIs() {
    console.log('\n🔍 Test des Autres APIs...');
    
    const apis = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            method: 'GET',
            name: 'Dashboard Stats'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/custom-urls/all',
            method: 'GET',
            name: 'URLs Personnalisées'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats',
            method: 'GET',
            name: 'Stats Monitoring'
        }
    ];
    
    let successCount = 0;
    
    for (const api of apis) {
        console.log(`\n🔍 Test: ${api.name}`);
        
        try {
            const response = await fetch(api.url, {
                method: api.method,
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            });
            
            console.log(`📊 Status: ${response.status}`);
            
            if (response.ok) {
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    console.log(`✅ ${api.name}: OK`);
                    successCount++;
                } catch (e) {
                    console.log(`⚠️ ${api.name}: JSON invalide`);
                }
            } else {
                console.log(`❌ ${api.name}: Erreur ${response.status}`);
            }
        } catch (error) {
            console.log(`💥 ${api.name}: Erreur réseau`);
        }
        
        // Pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    console.log(`\n📊 APIs testées: ${successCount}/${apis.length} fonctionnelles`);
    return successCount;
}

// Test de workflow complet
async function testCompleteWorkflow() {
    console.log('\n🔄 Test Workflow Complet...');
    
    console.log('📋 Étape 1: Sauvegarde des paramètres');
    const saveSuccess = await testRealSave();
    
    if (saveSuccess) {
        console.log('📋 Étape 2: Régénération des sitemaps');
        const regenSuccess = await testRealRegen();
        
        if (regenSuccess) {
            console.log('📋 Étape 3: Vérification des autres APIs');
            const apiCount = await testOtherAPIs();
            
            return {
                save: saveSuccess,
                regen: regenSuccess,
                apis: apiCount
            };
        }
    }
    
    return {
        save: saveSuccess,
        regen: false,
        apis: 0
    };
}

// Test principal
async function runRestoredTest() {
    console.log('🚀 Démarrage Test des Fonctionnalités Restaurées...\n');
    
    const startTime = Date.now();
    
    try {
        // Test du workflow complet
        const results = await testCompleteWorkflow();
        
        const endTime = Date.now();
        
        // Rapport final
        console.log('\n📊 === RAPPORT DES FONCTIONNALITÉS RESTAURÉES ===');
        console.log(`⏱️  Durée: ${endTime - startTime}ms`);
        console.log(`💾 Sauvegarde: ${results.save ? '✅ OK' : '❌ ERREUR'}`);
        console.log(`🔄 Régénération: ${results.regen ? '✅ OK' : '❌ ERREUR'}`);
        console.log(`🔍 Autres APIs: ${results.apis} fonctionnelles`);
        
        const allGood = results.save && results.regen && results.apis > 0;
        
        if (allGood) {
            console.log('\n🎉 === SUCCÈS COMPLET ! ===');
            console.log('✅ Toutes les fonctionnalités restaurées fonctionnent');
            console.log('✅ Les APIs retournent des données valides');
            console.log('✅ Le workflow complet est opérationnel');
            console.log('🚀 Prêt pour les fonctionnalités Phase 3 !');
        } else {
            console.log('\n⚠️ === SUCCÈS PARTIEL ===');
            console.log('🔧 Quelques fonctionnalités nécessitent encore des ajustements');
            
            if (!results.save) {
                console.log('❌ Problème avec la sauvegarde des paramètres');
            }
            if (!results.regen) {
                console.log('❌ Problème avec la régénération des sitemaps');
            }
            if (results.apis === 0) {
                console.log('❌ Problème avec les autres APIs');
            }
        }
        
        return results;
        
    } catch (error) {
        console.log('\n💥 Erreur lors du test:', error);
        return { success: false, error: error.message };
    }
}

// Rendre la fonction disponible globalement
window.testRestored = runRestoredTest;
window.testRealSave = testRealSave;
window.testRealRegen = testRealRegen;

console.log('\n🛠️ Fonctions disponibles:');
console.log('- testRestored() - Test complet des fonctionnalités restaurées');
console.log('- testRealSave() - Test de sauvegarde avec données réelles');
console.log('- testRealRegen() - Test de régénération avec données réelles');

console.log('\n🎯 Pour tester les fonctionnalités restaurées, tapez: testRestored()');

// Auto-exécution
console.log('\n🚀 Exécution automatique du test...\n');
runRestoredTest();
