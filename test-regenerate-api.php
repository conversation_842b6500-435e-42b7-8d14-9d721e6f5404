<?php
/**
 * Script de test pour l'API de régénération des sitemaps
 * 
 * Ce script teste directement l'endpoint de régénération pour identifier les erreurs.
 */

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier que l'utilisateur est connecté et a les permissions
if ( ! is_user_logged_in() || ! current_user_can( 'manage_options' ) ) {
    die( 'Accès refusé. Vous devez être connecté en tant qu\'administrateur.' );
}

echo "<h1>Test de l'API de régénération des sitemaps Boss SEO</h1>";

// Activer l'affichage des erreurs
error_reporting( E_ALL );
ini_set( 'display_errors', 1 );

// Tester la classe
try {
    echo "<h2>1. Test de chargement de la classe</h2>";
    
    if ( ! class_exists( 'Boss_Robots_Sitemap_Controller' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/technical/class-boss-robots-sitemap-controller.php' );
    }
    
    $controller = new Boss_Robots_Sitemap_Controller( 'boss-seo', '1.2.0' );
    echo "✅ Classe chargée avec succès<br>";
    
    echo "<h2>2. Test des permissions</h2>";
    $permissions = $controller->check_permissions();
    echo $permissions ? "✅ Permissions OK<br>" : "❌ Permissions insuffisantes<br>";
    
    echo "<h2>3. Test de récupération des paramètres</h2>";
    $settings = get_option( 'boss-seo_advanced_sitemap_settings', array() );
    echo "Paramètres actuels: <pre>" . print_r( $settings, true ) . "</pre>";
    
    echo "<h2>4. Test de régénération (simulation)</h2>";
    
    // Créer une fausse requête
    $request = new WP_REST_Request( 'POST', '/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate' );
    
    echo "Début du test de régénération...<br>";
    
    $result = $controller->regenerate_advanced_sitemap( $request );
    
    if ( is_wp_error( $result ) ) {
        echo "❌ Erreur WP: " . $result->get_error_message() . "<br>";
    } else {
        echo "✅ Régénération terminée<br>";
        echo "Résultat: <pre>" . print_r( $result->get_data(), true ) . "</pre>";
    }
    
} catch ( Exception $e ) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
    echo "Trace: <pre>" . $e->getTraceAsString() . "</pre>";
} catch ( Error $e ) {
    echo "❌ Erreur fatale: " . $e->getMessage() . "<br>";
    echo "Trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>5. Test des classes dépendantes</h2>";

$classes_to_test = array(
    'Boss_Sitemap_Dashboard',
    'Boss_Sitemap_Ping', 
    'Boss_Custom_Urls',
    'Boss_Sitemap_Monitoring',
    'Boss_Specialized_Sitemaps',
    'Boss_Smart_Optimization',
    'Boss_Search_Console',
    'Boss_Sitemap_Cache',
    'Boss_PDF_Reports'
);

foreach ( $classes_to_test as $class_name ) {
    if ( class_exists( $class_name ) ) {
        echo "✅ {$class_name} disponible<br>";
    } else {
        echo "❌ {$class_name} manquante<br>";
    }
}

echo "<h2>6. Test des fonctions WordPress</h2>";

$functions_to_test = array(
    'get_option',
    'update_option', 
    'wp_count_posts',
    'get_terms',
    'post_type_exists',
    'taxonomy_exists',
    'current_time',
    'rest_ensure_response'
);

foreach ( $functions_to_test as $function_name ) {
    if ( function_exists( $function_name ) ) {
        echo "✅ {$function_name} disponible<br>";
    } else {
        echo "❌ {$function_name} manquante<br>";
    }
}

echo "<h2>7. Logs récents</h2>";
echo "Vérifiez les logs d'erreur PHP pour plus de détails sur les erreurs.<br>";
echo "Chemin typique: /var/log/apache2/error.log ou dans wp-content/debug.log<br>";

?>
