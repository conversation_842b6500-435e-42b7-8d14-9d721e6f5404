<?php
/**
 * DIAGNOSTIC DES SITEMAPS
 * 
 * Identifie les problèmes avec les sitemaps générés
 * Accès : /wp-content/plugins/Bossseov1.1/sitemap-diagnostic.php
 */

// Headers HTML
header('Content-Type: text/html; charset=UTF-8');

echo '<h1>🔍 Diagnostic des Sitemaps Boss SEO</h1>';

// Vérifier les fichiers sitemap
$sitemap_files = array(
    'Minimal' => __DIR__ . '/sitemap-minimal.xml',
    'Urgence' => __DIR__ . '/boss-seo-sitemap.xml',
    'Professionnel' => __DIR__ . '/sitemaps/sitemap-index.xml'
);

echo '<h2>📁 Fichiers Sitemap</h2>';
echo '<table border="1" cellpadding="5">';
echo '<tr><th>Type</th><th>Fichier</th><th>Existe</th><th><PERSON>lle</th><th>XML Valide</th><th>Actions</th></tr>';

foreach ($sitemap_files as $type => $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    $valid = false;
    $content_preview = '';
    
    if ($exists && $size > 0) {
        $content = file_get_contents($file);
        $content_preview = htmlspecialchars(substr($content, 0, 200));
        
        // Vérifier XML
        $old_setting = libxml_use_internal_errors(true);
        $doc = simplexml_load_string($content);
        $valid = $doc !== false;
        libxml_use_internal_errors($old_setting);
    }
    
    echo '<tr>';
    echo '<td>' . $type . '</td>';
    echo '<td>' . basename($file) . '</td>';
    echo '<td>' . ($exists ? '✅' : '❌') . '</td>';
    echo '<td>' . ($size > 0 ? number_format($size) . ' bytes' : '-') . '</td>';
    echo '<td>' . ($valid ? '✅' : '❌') . '</td>';
    echo '<td>';
    if ($exists) {
        echo '<a href="' . plugins_url(basename($file), __FILE__) . '" target="_blank">Voir</a> | ';
        echo '<a href="?download=' . urlencode($file) . '">Télécharger</a>';
    }
    echo '</td>';
    echo '</tr>';
    
    if ($exists && !empty($content_preview)) {
        echo '<tr><td colspan="6"><small><strong>Aperçu:</strong><br><code>' . $content_preview . '</code></small></td></tr>';
    }
}

echo '</table>';

// Vérifier les scripts de génération
echo '<h2>🔧 Scripts de Génération</h2>';
$scripts = array(
    'Ultra-Robuste' => __DIR__ . '/boss-seo-ultra-robust-sitemap.php',
    'Professionnel' => __DIR__ . '/boss-seo-professional-sitemap.php',
    'Urgence' => __DIR__ . '/boss-seo-emergency-sitemap.php',
    'Minimal' => __DIR__ . '/boss-seo-minimal-sitemap.php'
);

echo '<table border="1" cellpadding="5">';
echo '<tr><th>Type</th><th>Fichier</th><th>Existe</th><th>Accessible</th><th>Test</th></tr>';

foreach ($scripts as $type => $script) {
    $exists = file_exists($script);
    $accessible = false;
    $test_result = '';
    
    if ($exists) {
        // Test d'accessibilité
        $url = plugins_url(basename($script), __FILE__) . '?action=generate';
        $context = stream_context_create(array(
            'http' => array(
                'timeout' => 5,
                'ignore_errors' => true
            )
        ));
        
        $response = @file_get_contents($url, false, $context);
        $accessible = $response !== false;
        
        if ($accessible) {
            $json = json_decode($response, true);
            if ($json) {
                $test_result = $json['success'] ? '✅ ' . $json['message'] : '❌ ' . $json['message'];
            } else {
                $test_result = '❌ Réponse non-JSON: ' . substr($response, 0, 100);
            }
        } else {
            $test_result = '❌ Inaccessible';
        }
    }
    
    echo '<tr>';
    echo '<td>' . $type . '</td>';
    echo '<td>' . basename($script) . '</td>';
    echo '<td>' . ($exists ? '✅' : '❌') . '</td>';
    echo '<td>' . ($accessible ? '✅' : '❌') . '</td>';
    echo '<td>' . $test_result . '</td>';
    echo '</tr>';
}

echo '</table>';

// Vérifier les dépendances Composer
echo '<h2>📦 Dépendances Composer</h2>';
$composer_file = __DIR__ . '/composer.json';
$vendor_dir = __DIR__ . '/vendor';
$autoload_file = __DIR__ . '/vendor/autoload.php';

echo '<ul>';
echo '<li>composer.json: ' . (file_exists($composer_file) ? '✅' : '❌') . '</li>';
echo '<li>vendor/: ' . (is_dir($vendor_dir) ? '✅' : '❌') . '</li>';
echo '<li>autoload.php: ' . (file_exists($autoload_file) ? '✅' : '❌') . '</li>';

if (file_exists($composer_file)) {
    $composer_content = json_decode(file_get_contents($composer_file), true);
    if ($composer_content && isset($composer_content['require'])) {
        echo '<li>Dépendances requises:</li>';
        echo '<ul>';
        foreach ($composer_content['require'] as $package => $version) {
            echo '<li>' . $package . ': ' . $version . '</li>';
        }
        echo '</ul>';
    }
}
echo '</ul>';

// Actions de test
echo '<h2>🧪 Tests Rapides</h2>';
echo '<p>';
echo '<a href="?test=minimal" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Tester Minimal</a> ';
echo '<a href="?test=emergency" style="background: #d63638; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Tester Urgence</a> ';
echo '<a href="?test=professional" style="background: #00a32a; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Tester Professionnel</a>';
echo '</p>';

// Traitement des actions
if (isset($_GET['test'])) {
    $test_type = $_GET['test'];
    echo '<h3>Résultat du test ' . $test_type . '</h3>';
    
    $script_map = array(
        'minimal' => 'boss-seo-minimal-sitemap.php',
        'emergency' => 'boss-seo-emergency-sitemap.php',
        'professional' => 'boss-seo-professional-sitemap.php'
    );
    
    if (isset($script_map[$test_type])) {
        $url = plugins_url($script_map[$test_type], __FILE__) . '?action=generate';
        $response = @file_get_contents($url);
        
        if ($response) {
            echo '<pre>' . htmlspecialchars($response) . '</pre>';
        } else {
            echo '<p style="color: red;">❌ Aucune réponse du script</p>';
        }
    }
}

// Téléchargement de fichier
if (isset($_GET['download']) && file_exists($_GET['download'])) {
    $file = $_GET['download'];
    header('Content-Type: application/xml');
    header('Content-Disposition: attachment; filename="' . basename($file) . '"');
    readfile($file);
    exit;
}

echo '<hr>';
echo '<p><small>Diagnostic généré le ' . date('Y-m-d H:i:s') . '</small></p>';
?>
