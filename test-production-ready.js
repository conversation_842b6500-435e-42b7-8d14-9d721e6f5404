/**
 * Script de test complet pour environnement de production (VPS)
 * À exécuter dans la console du navigateur
 */

console.log('🚀 Test Production Ready - Boss SEO Complet');
console.log('============================================');

// Test des APIs principales avec logique robuste
async function testProductionAPIs() {
    console.log('\n📡 === TEST APIS PRODUCTION ===\n');
    
    const apis = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
            method: 'POST',
            data: {
                enabled: true,
                includeImages: true,
                includeLastMod: true,
                defaultChangeFreq: 'weekly',
                defaultPriority: 0.8,
                includedPostTypes: ['post', 'page'],
                enableAutoUpdate: true,
                enableImageSitemap: true,
                enableAutoPing: true
            },
            name: 'Sauvegarde Paramètres Robuste'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
            method: 'POST',
            name: 'Régénération Complète'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            method: 'GET',
            name: 'Dashboard Stats'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/ping/all',
            method: 'POST',
            name: 'Ping Moteurs'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/custom-urls/all',
            method: 'GET',
            name: 'URLs Personnalisées'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats',
            method: 'GET',
            name: 'Monitoring Indexation'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/specialized/stats',
            method: 'GET',
            name: 'Sitemaps Spécialisés'
        }
    ];
    
    const results = {};
    let successCount = 0;
    
    for (const api of apis) {
        console.log(`🔍 Test: ${api.name}`);
        
        try {
            const options = {
                method: api.method,
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            };
            
            if (api.data && api.method !== 'GET') {
                options.headers['Content-Type'] = 'application/json';
                options.body = JSON.stringify(api.data);
            }
            
            const response = await fetch(api.url, options);
            
            console.log(`  📊 Status: ${response.status}`);
            
            if (response.ok) {
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    
                    if (json.success) {
                        console.log(`  ✅ ${api.name}: SUCCÈS`);
                        console.log(`     Message: ${json.message || 'OK'}`);
                        
                        if (json.data) {
                            const dataKeys = Object.keys(json.data);
                            console.log(`     Données: ${dataKeys.length} propriétés`);
                            
                            // Afficher des détails spécifiques
                            if (api.name.includes('Régénération') && json.data.regenerated) {
                                console.log(`     Sitemaps: ${json.data.regenerated}/${json.data.total}`);
                                console.log(`     URLs totales: ${json.data.total_urls || 'N/A'}`);
                            }
                            
                            if (api.name.includes('Dashboard') && json.data.sitemaps) {
                                console.log(`     Sitemaps actifs: ${Object.keys(json.data.sitemaps).length}`);
                            }
                        }
                        
                        results[api.name] = { success: true, data: json };
                        successCount++;
                    } else {
                        console.log(`  ⚠️ ${api.name}: Réponse avec erreur`);
                        console.log(`     Message: ${json.message || 'Erreur inconnue'}`);
                        results[api.name] = { success: false, error: json.message };
                    }
                } catch (e) {
                    console.log(`  ❌ ${api.name}: JSON invalide`);
                    console.log(`     Réponse: ${text.substring(0, 100)}...`);
                    results[api.name] = { success: false, error: 'JSON invalide' };
                }
            } else {
                console.log(`  ❌ ${api.name}: Erreur HTTP ${response.status}`);
                const text = await response.text();
                console.log(`     Erreur: ${text.substring(0, 100)}...`);
                results[api.name] = { success: false, error: `HTTP ${response.status}` };
            }
            
        } catch (error) {
            console.log(`  💥 ${api.name}: Erreur réseau`);
            console.log(`     Détail: ${error.message}`);
            results[api.name] = { success: false, error: error.message };
        }
        
        console.log('');
        
        // Pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`📊 Résultat: ${successCount}/${apis.length} APIs fonctionnelles`);
    return { results, successCount, total: apis.length };
}

// Test des APIs Phase 3 (nouvelles fonctionnalités)
async function testPhase3APIs() {
    console.log('\n🚀 === TEST APIS PHASE 3 ===\n');
    
    const phase3APIs = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities',
            method: 'GET',
            name: 'Priorités Intelligentes'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/algorithms',
            method: 'GET',
            name: 'Configuration Algorithmes'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/search-console/data',
            method: 'GET',
            name: 'Données Search Console'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/cache/stats',
            method: 'GET',
            name: 'Statistiques Cache'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/reports/templates',
            method: 'GET',
            name: 'Templates Rapports PDF'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/reports/history',
            method: 'GET',
            name: 'Historique Rapports'
        }
    ];
    
    let phase3Success = 0;
    
    for (const api of phase3APIs) {
        console.log(`🔍 Test Phase 3: ${api.name}`);
        
        try {
            const response = await fetch(api.url, {
                method: api.method,
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            });
            
            console.log(`  📊 Status: ${response.status}`);
            
            if (response.ok) {
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    console.log(`  ✅ ${api.name}: Disponible`);
                    phase3Success++;
                } catch (e) {
                    console.log(`  ⚠️ ${api.name}: JSON invalide`);
                }
            } else {
                console.log(`  ❌ ${api.name}: Non disponible (${response.status})`);
            }
        } catch (error) {
            console.log(`  💥 ${api.name}: Erreur réseau`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    console.log(`\n📊 APIs Phase 3: ${phase3Success}/${phase3APIs.length} disponibles`);
    return phase3Success;
}

// Test de workflow complet
async function testCompleteWorkflow() {
    console.log('\n🔄 === TEST WORKFLOW COMPLET ===\n');
    
    console.log('📋 Étape 1: Configuration des paramètres');
    const configResult = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings', {
        method: 'POST',
        headers: {
            'X-WP-Nonce': wpApiSettings.nonce,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            enabled: true,
            includeImages: true,
            defaultChangeFreq: 'weekly',
            defaultPriority: 0.8,
            includedPostTypes: ['post', 'page'],
            enableAutoUpdate: true
        })
    });
    
    const configSuccess = configResult.ok;
    console.log(`✅ Configuration: ${configSuccess ? 'OK' : 'ERREUR'}`);
    
    if (configSuccess) {
        console.log('📋 Étape 2: Régénération des sitemaps');
        const regenResult = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        
        const regenSuccess = regenResult.ok;
        console.log(`✅ Régénération: ${regenSuccess ? 'OK' : 'ERREUR'}`);
        
        if (regenSuccess) {
            console.log('📋 Étape 3: Ping des moteurs de recherche');
            const pingResult = await fetch('/wp-json/boss-seo/v1/robots-sitemap/ping/all', {
                method: 'POST',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            });
            
            const pingSuccess = pingResult.ok;
            console.log(`✅ Ping moteurs: ${pingSuccess ? 'OK' : 'ERREUR'}`);
            
            return { config: configSuccess, regen: regenSuccess, ping: pingSuccess };
        }
    }
    
    return { config: configSuccess, regen: false, ping: false };
}

// Test principal pour production
async function runProductionTest() {
    console.log('🚀 Démarrage Test Production Complet...\n');
    
    const startTime = Date.now();
    
    try {
        // Vérifier les prérequis
        if (typeof wpApiSettings === 'undefined' || !wpApiSettings.nonce) {
            console.log('❌ Configuration WordPress manquante');
            return false;
        }
        
        console.log('✅ Prérequis OK');
        console.log(`🔑 Nonce: ${wpApiSettings.nonce.substring(0, 10)}...`);
        console.log(`🌐 Site: ${window.location.origin}`);
        
        // Tests principaux
        const apiResults = await testProductionAPIs();
        const phase3Count = await testPhase3APIs();
        const workflowResults = await testCompleteWorkflow();
        
        const endTime = Date.now();
        
        // Rapport final
        console.log('\n📊 === RAPPORT FINAL PRODUCTION ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        console.log(`📡 APIs principales: ${apiResults.successCount}/${apiResults.total}`);
        console.log(`🚀 APIs Phase 3: ${phase3Count}/6`);
        console.log(`🔄 Workflow: ${Object.values(workflowResults).filter(r => r).length}/3 étapes`);
        
        const overallSuccess = (apiResults.successCount / apiResults.total) > 0.8 && 
                              workflowResults.config && workflowResults.regen;
        
        if (overallSuccess) {
            console.log('\n🎉 === SUCCÈS PRODUCTION ! ===');
            console.log('✅ Boss SEO est entièrement opérationnel en production');
            console.log('✅ Toutes les fonctionnalités principales fonctionnent');
            console.log('✅ Le workflow complet est validé');
            console.log('🚀 Prêt pour utilisation en production !');
            
            // Recommandations pour la production
            console.log('\n💡 === RECOMMANDATIONS PRODUCTION ===');
            console.log('🔧 Activez la mise à jour automatique des sitemaps');
            console.log('🔧 Configurez le ping automatique des moteurs');
            console.log('🔧 Surveillez les logs pour les erreurs');
            console.log('🔧 Testez régulièrement les URLs de sitemaps');
            
        } else {
            console.log('\n⚠️ === PROBLÈMES DÉTECTÉS ===');
            console.log('🔧 Quelques fonctionnalités nécessitent des ajustements');
            
            if (apiResults.successCount < apiResults.total) {
                console.log(`❌ ${apiResults.total - apiResults.successCount} APIs en erreur`);
            }
            if (!workflowResults.config) {
                console.log('❌ Problème avec la sauvegarde des paramètres');
            }
            if (!workflowResults.regen) {
                console.log('❌ Problème avec la régénération des sitemaps');
            }
        }
        
        return {
            success: overallSuccess,
            apis: apiResults,
            phase3: phase3Count,
            workflow: workflowResults
        };
        
    } catch (error) {
        console.log('\n💥 Erreur lors du test production:', error);
        return { success: false, error: error.message };
    }
}

// Rendre disponible globalement
window.testProduction = runProductionTest;
window.testProdAPIs = testProductionAPIs;
window.testWorkflow = testCompleteWorkflow;

console.log('\n🛠️ Fonctions de test production disponibles:');
console.log('- testProduction() - Test complet pour production');
console.log('- testProdAPIs() - Test des APIs principales');
console.log('- testWorkflow() - Test du workflow complet');

console.log('\n🎯 Pour tester en production, tapez: testProduction()');
console.log('🚀 Ce test validera toutes les fonctionnalités Boss SEO !');
