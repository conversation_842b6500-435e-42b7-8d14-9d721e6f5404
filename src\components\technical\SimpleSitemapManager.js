import React, { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, CheckboxControl, Dashicon, Notice } from '@wordpress/components';
import RobotsSitemapService from '../../services/RobotsSitemapService';

/**
 * Gestionnaire de sitemap simple et efficace
 */
const SimpleSitemapManager = () => {
  const [settings, setSettings] = useState({
    includedPostTypes: ['post', 'page'],
    includedTaxonomies: ['category']
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');
  const [lastGeneration, setLastGeneration] = useState(null);

  // Charger les paramètres au montage
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const response = await RobotsSitemapService.getAdvancedSitemapSettings();
      if (response.success) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Erreur chargement paramètres:', error);
      showMessage('Erreur lors du chargement des paramètres', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsSaving(true);
      const response = await RobotsSitemapService.saveAdvancedSitemapSettings(settings);
      if (response.success) {
        showMessage('Paramètres sauvegardés avec succès', 'success');
      }
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
      showMessage('Erreur lors de la sauvegarde', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const generateSitemap = async () => {
    try {
      setIsGenerating(true);
      const response = await RobotsSitemapService.regenerateSitemap();
      if (response.success) {
        setLastGeneration(response);
        showMessage(`Sitemap généré avec succès - ${response.total_urls} URLs`, 'success');
      }
    } catch (error) {
      console.error('Erreur génération:', error);
      showMessage('Erreur lors de la génération du sitemap', 'error');
    } finally {
      setIsGenerating(false);
    }
  };

  const showMessage = (text, type) => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => {
      setMessage('');
      setMessageType('');
    }, 5000);
  };

  const handlePostTypeChange = (postType, checked) => {
    setSettings(prev => ({
      ...prev,
      includedPostTypes: checked 
        ? [...prev.includedPostTypes, postType]
        : prev.includedPostTypes.filter(type => type !== postType)
    }));
  };

  const handleTaxonomyChange = (taxonomy, checked) => {
    setSettings(prev => ({
      ...prev,
      includedTaxonomies: checked 
        ? [...prev.includedTaxonomies, taxonomy]
        : prev.includedTaxonomies.filter(tax => tax !== taxonomy)
    }));
  };

  if (isLoading) {
    return (
      <div className="boss-flex boss-items-center boss-justify-center boss-p-8">
        <Dashicon icon="update" className="boss-animate-spin boss-text-blue-600 boss-text-2xl" />
        <span className="boss-ml-2">{__('Chargement...', 'boss-seo')}</span>
      </div>
    );
  }

  return (
    <div className="boss-max-w-4xl boss-mx-auto boss-p-6">
      {/* En-tête */}
      <div className="boss-mb-8">
        <h1 className="boss-text-3xl boss-font-bold boss-text-gray-900 boss-mb-2">
          🗺️ {__('Gestionnaire de Sitemap', 'boss-seo')}
        </h1>
        <p className="boss-text-gray-600">
          {__('Générateur simple et efficace utilisant samdark/sitemap', 'boss-seo')}
        </p>
      </div>

      {/* Messages */}
      {message && (
        <Notice 
          status={messageType} 
          isDismissible={false}
          className="boss-mb-6"
        >
          {message}
        </Notice>
      )}

      {/* Résultat de la dernière génération */}
      {lastGeneration && (
        <Card className="boss-mb-6 boss-border-green-200 boss-bg-green-50">
          <CardHeader className="boss-bg-green-100">
            <h2 className="boss-text-lg boss-font-semibold boss-text-green-800">
              ✅ {__('Dernière génération réussie', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-4">
              <div>
                <span className="boss-font-medium">{__('URLs:', 'boss-seo')}</span> {lastGeneration.total_urls}
              </div>
              <div>
                <span className="boss-font-medium">{__('Temps:', 'boss-seo')}</span> {lastGeneration.generation_time}s
              </div>
              <div>
                <span className="boss-font-medium">{__('Date:', 'boss-seo')}</span> {lastGeneration.timestamp}
              </div>
            </div>
            {lastGeneration.sitemap_url && (
              <div className="boss-p-3 boss-bg-white boss-border boss-border-green-300 boss-rounded-md">
                <div className="boss-flex boss-items-center boss-justify-between">
                  <div>
                    <span className="boss-font-medium boss-text-green-700">🔗 {__('URL du sitemap:', 'boss-seo')}</span>
                    <br />
                    <a 
                      href={lastGeneration.sitemap_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="boss-text-blue-600 boss-underline boss-break-all"
                    >
                      {lastGeneration.sitemap_url}
                    </a>
                  </div>
                  <Button
                    isSecondary
                    onClick={() => window.open(lastGeneration.sitemap_url, '_blank')}
                    className="boss-ml-2"
                  >
                    👁️ {__('Voir', 'boss-seo')}
                  </Button>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6">
        {/* Paramètres */}
        <Card>
          <CardHeader className="boss-bg-gradient-to-r boss-from-blue-50 boss-to-indigo-50">
            <h2 className="boss-text-lg boss-font-semibold boss-text-blue-800">
              ⚙️ {__('Paramètres', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <div>
                <h3 className="boss-font-medium boss-mb-3">{__('Types de contenu', 'boss-seo')}</h3>
                <div className="boss-space-y-2">
                  <CheckboxControl
                    label={__('Articles', 'boss-seo')}
                    checked={settings.includedPostTypes.includes('post')}
                    onChange={(checked) => handlePostTypeChange('post', checked)}
                  />
                  <CheckboxControl
                    label={__('Pages', 'boss-seo')}
                    checked={settings.includedPostTypes.includes('page')}
                    onChange={(checked) => handlePostTypeChange('page', checked)}
                  />
                </div>
              </div>

              <div>
                <h3 className="boss-font-medium boss-mb-3">{__('Taxonomies', 'boss-seo')}</h3>
                <div className="boss-space-y-2">
                  <CheckboxControl
                    label={__('Catégories', 'boss-seo')}
                    checked={settings.includedTaxonomies.includes('category')}
                    onChange={(checked) => handleTaxonomyChange('category', checked)}
                  />
                </div>
              </div>

              <Button
                isPrimary
                onClick={saveSettings}
                isBusy={isSaving}
                disabled={isSaving}
                className="boss-w-full"
              >
                {isSaving ? __('Sauvegarde...', 'boss-seo') : __('💾 Sauvegarder', 'boss-seo')}
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader className="boss-bg-gradient-to-r boss-from-green-50 boss-to-emerald-50">
            <h2 className="boss-text-lg boss-font-semibold boss-text-green-800">
              🚀 {__('Actions', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <div className="boss-text-center">
                <Button
                  isPrimary
                  onClick={generateSitemap}
                  isBusy={isGenerating}
                  disabled={isGenerating}
                  className="boss-w-full boss-h-12 boss-text-lg"
                >
                  {isGenerating ? (
                    <>
                      <Dashicon icon="update" className="boss-animate-spin boss-mr-2" />
                      {__('Génération...', 'boss-seo')}
                    </>
                  ) : (
                    <>
                      🗺️ {__('Générer le sitemap', 'boss-seo')}
                    </>
                  )}
                </Button>
              </div>

              {lastGeneration?.sitemap_url && (
                <div className="boss-space-y-2">
                  <Button
                    isSecondary
                    onClick={() => window.open(lastGeneration.sitemap_url, '_blank')}
                    className="boss-w-full"
                  >
                    👁️ {__('Voir le sitemap', 'boss-seo')}
                  </Button>
                  <Button
                    isSecondary
                    onClick={() => {
                      navigator.clipboard.writeText(lastGeneration.sitemap_url);
                      showMessage('URL copiée dans le presse-papiers', 'success');
                    }}
                    className="boss-w-full"
                  >
                    📋 {__('Copier l\'URL', 'boss-seo')}
                  </Button>
                </div>
              )}

              <div className="boss-mt-6 boss-p-4 boss-bg-gray-50 boss-rounded-lg">
                <h4 className="boss-font-medium boss-mb-2">{__('ℹ️ Informations', 'boss-seo')}</h4>
                <ul className="boss-text-sm boss-text-gray-600 boss-space-y-1">
                  <li>• {__('Utilise samdark/sitemap pour une génération optimisée', 'boss-seo')}</li>
                  <li>• {__('Génère un index de sitemaps pour une meilleure organisation', 'boss-seo')}</li>
                  <li>• {__('Compatible avec tous les moteurs de recherche', 'boss-seo')}</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default SimpleSitemapManager;
