<?php
/**
 * Gestionnaire simple de sitemap Boss SEO
 * 
 * Ce fichier gère la redirection du sitemap principal vers notre générateur.
 */

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier si c'est une requête pour le sitemap
if ( isset( $_GET['sitemap'] ) || strpos( $_SERVER['REQUEST_URI'], 'sitemap' ) !== false ) {
    
    // Générer le sitemap simple
    header( 'Content-Type: application/xml; charset=UTF-8' );
    header( 'Cache-Control: public, max-age=3600' );
    
    echo generate_boss_seo_sitemap();
    exit;
}

/**
 * Génère un sitemap XML simple et fonctionnel.
 */
function generate_boss_seo_sitemap() {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Page d'accueil
    $xml .= '<url>' . "\n";
    $xml .= '<loc>' . esc_url( home_url( '/' ) ) . '</loc>' . "\n";
    $xml .= '<lastmod>' . date( 'c' ) . '</lastmod>' . "\n";
    $xml .= '<changefreq>daily</changefreq>' . "\n";
    $xml .= '<priority>1.0</priority>' . "\n";
    $xml .= '</url>' . "\n";
    
    // Pages
    $pages = get_posts( array(
        'post_type' => 'page',
        'post_status' => 'publish',
        'numberposts' => 100,
        'orderby' => 'date',
        'order' => 'DESC'
    ) );
    
    foreach ( $pages as $page ) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . esc_url( get_permalink( $page->ID ) ) . '</loc>' . "\n";
        $xml .= '<lastmod>' . date( 'c', strtotime( $page->post_modified ) ) . '</lastmod>' . "\n";
        $xml .= '<changefreq>weekly</changefreq>' . "\n";
        $xml .= '<priority>0.8</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }
    
    // Articles
    $posts = get_posts( array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => 200,
        'orderby' => 'date',
        'order' => 'DESC'
    ) );
    
    foreach ( $posts as $post ) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . esc_url( get_permalink( $post->ID ) ) . '</loc>' . "\n";
        $xml .= '<lastmod>' . date( 'c', strtotime( $post->post_modified ) ) . '</lastmod>' . "\n";
        $xml .= '<changefreq>weekly</changefreq>' . "\n";
        $xml .= '<priority>0.7</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }
    
    // Catégories
    $categories = get_categories( array( 'hide_empty' => true ) );
    foreach ( $categories as $category ) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . esc_url( get_category_link( $category->term_id ) ) . '</loc>' . "\n";
        $xml .= '<lastmod>' . date( 'c' ) . '</lastmod>' . "\n";
        $xml .= '<changefreq>weekly</changefreq>' . "\n";
        $xml .= '<priority>0.5</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }
    
    $xml .= '</urlset>';
    
    return $xml;
}

// Ajouter une règle de réécriture pour le sitemap
add_action( 'init', 'boss_seo_add_sitemap_rewrite_rule' );
function boss_seo_add_sitemap_rewrite_rule() {
    add_rewrite_rule( '^sitemap\.xml$', 'index.php?boss_seo_sitemap=1', 'top' );
    add_rewrite_rule( '^sitemap_index\.xml$', 'index.php?boss_seo_sitemap=1', 'top' );
}

// Ajouter la variable de requête
add_filter( 'query_vars', 'boss_seo_add_query_vars' );
function boss_seo_add_query_vars( $vars ) {
    $vars[] = 'boss_seo_sitemap';
    return $vars;
}

// Gérer la requête du sitemap
add_action( 'template_redirect', 'boss_seo_handle_sitemap_request' );
function boss_seo_handle_sitemap_request() {
    if ( get_query_var( 'boss_seo_sitemap' ) ) {
        header( 'Content-Type: application/xml; charset=UTF-8' );
        header( 'Cache-Control: public, max-age=3600' );
        
        echo generate_boss_seo_sitemap();
        exit;
    }
}

?>
