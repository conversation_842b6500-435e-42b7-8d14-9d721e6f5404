<?php
/**
 * GÉNÉRATEUR DE SITEMAP PROFESSIONNEL avec samdark/sitemap
 * 
 * Installation : composer install
 * Accès : /wp-content/plugins/Bossseov1.1/boss-seo-professional-sitemap.php?action=generate
 */

// Inclure WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Vérifier si Composer est installé
if ( ! file_exists( __DIR__ . '/vendor/autoload.php' ) ) {
    wp_die( '
        <h1>📦 Installation requise</h1>
        <p>Veuillez installer les dépendances Composer :</p>
        <pre>cd ' . __DIR__ . ' && composer install</pre>
        <p>Ou utilisez la <a href="boss-seo-emergency-sitemap.php">solution d\'urgence</a></p>
    ' );
}

// Charger Composer
require_once __DIR__ . '/vendor/autoload.php';

use samdark\sitemap\Sitemap;
use samdark\sitemap\Index;

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Accès refusé' );
}

$action = isset( $_GET['action'] ) ? $_GET['action'] : '';

switch ( $action ) {
    case 'generate':
        generate_professional_sitemap();
        break;
    case 'save_settings':
        save_professional_settings();
        break;
    case 'get_settings':
        get_professional_settings();
        break;
    default:
        show_professional_interface();
        break;
}

function generate_professional_sitemap() {
    try {
        $upload_dir = wp_upload_dir();
        $sitemap_dir = $upload_dir['basedir'] . '/sitemaps/';
        
        // Créer le dossier s'il n'existe pas
        if ( ! file_exists( $sitemap_dir ) ) {
            wp_mkdir_p( $sitemap_dir );
        }
        
        $settings = get_option( 'boss_seo_professional_settings', array(
            'include_posts' => true,
            'include_pages' => true,
            'include_categories' => true,
            'include_tags' => false,
            'max_urls_per_sitemap' => 50000
        ) );
        
        // Créer l'index des sitemaps
        $index = new Index( $sitemap_dir . 'sitemap-index.xml' );
        
        $total_urls = 0;
        $sitemaps_created = array();
        
        // Sitemap principal (pages importantes)
        $main_sitemap = new Sitemap( $sitemap_dir . 'sitemap-main.xml' );
        
        // Page d'accueil
        $main_sitemap->addItem( home_url( '/' ), time(), Sitemap::DAILY, 1.0 );
        $total_urls++;
        
        // Pages
        if ( $settings['include_pages'] ) {
            $pages = get_posts( array(
                'post_type' => 'page',
                'post_status' => 'publish',
                'numberposts' => -1
            ) );
            
            foreach ( $pages as $page ) {
                $main_sitemap->addItem(
                    get_permalink( $page->ID ),
                    strtotime( $page->post_modified ),
                    Sitemap::WEEKLY,
                    0.8
                );
                $total_urls++;
            }
        }
        
        $main_sitemap->write();
        $index->addSitemap( $upload_dir['baseurl'] . '/sitemaps/sitemap-main.xml' );
        $sitemaps_created[] = 'sitemap-main.xml';
        
        // Sitemap des articles
        if ( $settings['include_posts'] ) {
            $posts_sitemap = new Sitemap( $sitemap_dir . 'sitemap-posts.xml' );
            
            $posts = get_posts( array(
                'post_type' => 'post',
                'post_status' => 'publish',
                'numberposts' => $settings['max_urls_per_sitemap']
            ) );
            
            foreach ( $posts as $post ) {
                $posts_sitemap->addItem(
                    get_permalink( $post->ID ),
                    strtotime( $post->post_modified ),
                    Sitemap::WEEKLY,
                    0.7
                );
                $total_urls++;
            }
            
            $posts_sitemap->write();
            $index->addSitemap( $upload_dir['baseurl'] . '/sitemaps/sitemap-posts.xml' );
            $sitemaps_created[] = 'sitemap-posts.xml';
        }
        
        // Sitemap des catégories
        if ( $settings['include_categories'] ) {
            $categories_sitemap = new Sitemap( $sitemap_dir . 'sitemap-categories.xml' );
            
            $categories = get_categories( array( 'hide_empty' => true ) );
            foreach ( $categories as $category ) {
                $categories_sitemap->addItem(
                    get_category_link( $category->term_id ),
                    time(),
                    Sitemap::WEEKLY,
                    0.5
                );
                $total_urls++;
            }
            
            $categories_sitemap->write();
            $index->addSitemap( $upload_dir['baseurl'] . '/sitemaps/sitemap-categories.xml' );
            $sitemaps_created[] = 'sitemap-categories.xml';
        }
        
        // Sitemap des tags
        if ( $settings['include_tags'] ) {
            $tags_sitemap = new Sitemap( $sitemap_dir . 'sitemap-tags.xml' );
            
            $tags = get_tags( array( 'hide_empty' => true ) );
            foreach ( $tags as $tag ) {
                $tags_sitemap->addItem(
                    get_tag_link( $tag->term_id ),
                    time(),
                    Sitemap::MONTHLY,
                    0.3
                );
                $total_urls++;
            }
            
            $tags_sitemap->write();
            $index->addSitemap( $upload_dir['baseurl'] . '/sitemaps/sitemap-tags.xml' );
            $sitemaps_created[] = 'sitemap-tags.xml';
        }
        
        // Écrire l'index
        $index->write();
        
        // Mettre à jour les options
        update_option( 'boss_seo_last_sitemap_generation', current_time( 'mysql' ) );
        update_option( 'boss_seo_sitemap_index_url', $upload_dir['baseurl'] . '/sitemaps/sitemap-index.xml' );
        
        // Réponse JSON
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => true,
            'message' => "Sitemaps générés avec succès - {$total_urls} URLs",
            'sitemap_index_url' => $upload_dir['baseurl'] . '/sitemaps/sitemap-index.xml',
            'total_urls' => $total_urls,
            'sitemaps_created' => $sitemaps_created,
            'generation_time' => current_time( 'mysql' )
        ) );
        
    } catch ( Exception $e ) {
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        ) );
    }
    exit;
}

function save_professional_settings() {
    try {
        $settings = array(
            'include_posts' => isset( $_POST['include_posts'] ),
            'include_pages' => isset( $_POST['include_pages'] ),
            'include_categories' => isset( $_POST['include_categories'] ),
            'include_tags' => isset( $_POST['include_tags'] ),
            'max_urls_per_sitemap' => intval( $_POST['max_urls_per_sitemap'] ?? 50000 ),
            'last_updated' => current_time( 'mysql' )
        );
        
        update_option( 'boss_seo_professional_settings', $settings );
        
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => true,
            'message' => 'Paramètres sauvegardés',
            'settings' => $settings
        ) );
    } catch ( Exception $e ) {
        header( 'Content-Type: application/json' );
        echo json_encode( array(
            'success' => false,
            'message' => $e->getMessage()
        ) );
    }
    exit;
}

function get_professional_settings() {
    $settings = get_option( 'boss_seo_professional_settings', array(
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => true,
        'include_tags' => false,
        'max_urls_per_sitemap' => 50000
    ) );
    
    header( 'Content-Type: application/json' );
    echo json_encode( array(
        'success' => true,
        'settings' => $settings
    ) );
    exit;
}

function show_professional_interface() {
    $settings = get_option( 'boss_seo_professional_settings', array(
        'include_posts' => true,
        'include_pages' => true,
        'include_categories' => true,
        'include_tags' => false,
        'max_urls_per_sitemap' => 50000
    ) );
    
    $last_generation = get_option( 'boss_seo_last_sitemap_generation', 'Jamais' );
    $sitemap_index_url = get_option( 'boss_seo_sitemap_index_url', '' );
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Boss SEO - Générateur Professionnel</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: #f1f1f1; }
            .container { max-width: 1000px; margin: 20px auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
            .content { padding: 30px; }
            .button { background: #0073aa; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px; transition: all 0.3s; }
            .button:hover { background: #005a87; transform: translateY(-1px); }
            .button.success { background: #46b450; }
            .button.danger { background: #dc3232; }
            .success { color: #46b450; font-weight: bold; }
            .error { color: #dc3232; font-weight: bold; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #0073aa; }
            .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
            .card { background: #f9f9f9; padding: 20px; border-radius: 6px; border: 1px solid #ddd; }
            input[type="number"] { width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
            label { display: block; margin: 10px 0; font-weight: 500; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Boss SEO - Générateur Professionnel</h1>
                <p>Powered by samdark/sitemap - Solution industrielle</p>
            </div>
            
            <div class="content">
                <div class="info">
                    <strong>📊 Statut :</strong><br>
                    <strong>Dernière génération :</strong> <?php echo $last_generation; ?><br>
                    <?php if ( $sitemap_index_url ): ?>
                    <strong>Index des sitemaps :</strong> <a href="<?php echo $sitemap_index_url; ?>" target="_blank"><?php echo $sitemap_index_url; ?></a>
                    <?php endif; ?>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <h2>⚙️ Configuration</h2>
                        <form id="settingsForm">
                            <label><input type="checkbox" name="include_pages" <?php checked( $settings['include_pages'] ); ?>> 📄 Inclure les pages</label>
                            <label><input type="checkbox" name="include_posts" <?php checked( $settings['include_posts'] ); ?>> 📝 Inclure les articles</label>
                            <label><input type="checkbox" name="include_categories" <?php checked( $settings['include_categories'] ); ?>> 📂 Inclure les catégories</label>
                            <label><input type="checkbox" name="include_tags" <?php checked( $settings['include_tags'] ); ?>> 🏷️ Inclure les tags</label>
                            <label>
                                📊 URLs max par sitemap : 
                                <input type="number" name="max_urls_per_sitemap" value="<?php echo $settings['max_urls_per_sitemap']; ?>" min="1000" max="50000">
                            </label>
                            <button type="button" class="button" onclick="saveSettings()">💾 Sauvegarder</button>
                        </form>
                    </div>
                    
                    <div class="card">
                        <h2>🎯 Actions</h2>
                        <button class="button success" onclick="generateSitemap()">🚀 Générer les sitemaps</button>
                        <button class="button" onclick="window.open('<?php echo $sitemap_index_url; ?>', '_blank')">👁️ Voir l'index</button>
                        <button class="button" onclick="window.open('boss-seo-emergency-sitemap.php', '_blank')">🚨 Solution d'urgence</button>
                        <button class="button danger" onclick="if(confirm('Installer Composer ?')) window.open('https://getcomposer.org/download/', '_blank')">📦 Installer Composer</button>
                    </div>
                </div>
                
                <div id="result"></div>
            </div>
        </div>
        
        <script>
        function generateSitemap() {
            document.getElementById('result').innerHTML = '<div class="info">⏳ Génération en cours...</div>';
            
            fetch('?action=generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = `
                            <div class="info">
                                <div class="success">✅ ${data.message}</div>
                                <strong>📊 Détails :</strong><br>
                                • URLs totales : ${data.total_urls}<br>
                                • Sitemaps créés : ${data.sitemaps_created.join(', ')}<br>
                                • Heure : ${data.generation_time}
                            </div>
                        `;
                        setTimeout(() => location.reload(), 3000);
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="error">❌ Erreur: ' + error.message + '</div>';
                });
        }
        
        function saveSettings() {
            const form = document.getElementById('settingsForm');
            const formData = new FormData(form);
            formData.append('action', 'save_settings');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('result').innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                });
        }
        </script>
    </body>
    </html>
    <?php
}
?>
